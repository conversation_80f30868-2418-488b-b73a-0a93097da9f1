2025-08-03 00:03:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 00:03:31.533 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 00:08:30.487 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 00:08:31.549 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 00:13:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 00:13:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 00:18:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 00:18:31.534 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 00:23:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 00:23:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 00:28:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 00:28:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 00:33:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 00:33:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 00:38:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 00:38:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 00:43:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 00:43:31.512 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 00:48:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 00:48:31.514 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 00:53:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 00:53:31.516 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 00:58:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 00:58:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 01:03:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 01:03:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 01:08:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 01:08:31.513 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 01:13:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 01:13:31.523 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 01:18:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 01:18:31.517 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 01:23:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 01:23:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 01:28:30.483 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 01:28:31.530 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 01:33:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 01:33:31.533 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 01:38:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 01:38:31.517 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 01:43:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 01:43:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 01:48:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 01:48:31.513 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 01:53:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 01:53:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 01:58:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 01:58:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 02:03:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 02:03:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 02:08:30.465 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 02:08:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 02:13:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 02:13:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 02:18:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 02:18:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 02:23:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 02:23:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 02:28:30.489 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 02:28:31.552 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 02:33:30.486 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 02:33:31.538 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 02:38:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 02:38:31.533 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 02:43:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 02:43:31.542 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 02:48:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 02:48:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 02:53:30.483 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 02:53:31.546 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 02:58:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 02:58:31.545 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 03:03:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 03:03:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 03:08:30.485 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 03:08:31.539 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 03:13:30.485 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 03:13:31.539 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 03:18:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 03:18:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 03:23:30.482 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 03:23:31.553 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 03:28:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 03:28:31.538 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 03:33:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 03:33:31.542 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 03:38:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 03:38:31.514 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 03:43:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 03:43:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 03:48:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 03:48:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 03:53:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 03:53:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 03:58:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 03:58:33.882 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 04:03:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 04:03:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 04:08:32.862 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 04:08:33.918 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 04:13:32.840 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 04:13:33.889 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 04:18:32.839 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 04:18:33.888 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 04:23:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 04:23:33.892 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 04:28:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 04:28:33.886 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 04:33:32.842 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 04:33:33.890 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 04:38:32.843 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 04:38:33.890 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 04:43:32.839 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 04:43:33.889 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 04:48:32.836 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 04:48:33.887 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 04:53:32.840 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 04:53:33.892 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 04:58:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 04:58:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 05:03:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 05:03:33.887 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 05:08:32.835 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 05:08:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 05:13:32.844 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 05:13:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 05:18:32.850 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 05:18:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 05:23:32.844 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 05:23:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 05:28:32.841 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 05:28:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 05:33:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 05:33:33.887 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 05:38:32.836 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 05:38:33.892 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 05:43:32.835 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 05:43:33.888 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 05:48:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 05:48:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 05:53:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 05:53:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 05:58:32.837 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 05:58:33.887 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 06:03:32.837 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 06:03:33.887 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 06:08:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 06:08:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 06:13:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 06:13:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 06:18:32.844 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 06:18:33.888 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 06:23:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 06:23:33.892 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 06:28:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 06:28:33.892 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 06:33:32.837 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 06:33:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 06:38:32.835 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 06:38:33.880 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 06:43:32.843 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 06:43:33.896 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 06:48:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 06:48:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 06:53:32.850 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 06:53:33.896 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 06:58:32.842 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 06:58:33.888 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 07:03:32.846 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 07:03:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 07:08:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 07:08:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 07:13:32.845 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 07:13:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 07:18:32.842 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 07:18:33.895 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 07:23:32.851 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 07:23:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 07:28:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 07:28:33.884 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 07:33:32.839 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 07:33:33.890 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 07:38:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 07:38:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 07:43:32.850 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 07:43:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 07:48:32.840 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 07:48:33.885 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 07:53:32.851 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 07:53:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 07:58:32.843 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 07:58:33.889 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 08:03:32.836 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 08:03:33.878 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 08:08:32.845 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 08:08:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 08:13:32.837 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 08:13:33.882 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 08:18:32.839 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 08:18:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 08:23:32.844 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 08:23:33.896 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 08:28:32.844 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 08:28:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 08:33:32.837 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 08:33:33.890 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 08:38:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 08:38:33.894 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 08:43:32.846 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 08:43:33.894 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 08:48:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 08:48:33.902 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 08:53:32.844 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 08:53:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 08:58:32.841 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 08:58:33.889 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 09:03:32.836 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 09:03:33.883 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 09:08:32.849 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 09:08:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 09:13:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 09:13:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 09:18:32.845 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 09:18:33.890 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 09:23:32.834 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 09:23:33.879 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 09:28:32.840 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 09:28:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 09:33:32.849 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 09:33:33.894 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 09:38:32.851 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 09:38:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 09:43:32.844 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 09:43:33.888 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 09:48:32.845 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 09:48:33.895 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 09:53:32.850 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 09:53:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 09:58:32.845 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 09:58:33.888 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 10:03:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 10:03:33.881 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 10:08:32.840 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 10:08:33.894 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 10:13:32.850 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 10:13:33.906 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 10:18:32.849 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 10:18:33.895 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 10:23:32.849 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 10:23:33.895 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 10:28:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 10:28:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 10:33:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 10:33:33.895 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 10:38:32.850 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 10:38:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 10:43:32.849 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 10:43:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 10:48:32.840 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 10:48:33.890 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 10:53:32.843 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 10:53:33.894 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 10:58:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 10:58:33.882 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 11:03:32.842 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 11:03:33.896 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 11:08:32.840 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 11:08:33.888 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 11:13:32.843 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 11:13:33.890 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 11:18:32.844 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 11:18:33.896 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 11:23:32.849 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 11:23:33.894 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 11:28:32.850 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 11:28:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 11:33:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 11:33:33.882 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 11:38:32.843 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 11:38:33.896 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 11:43:32.837 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 11:43:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 11:48:32.843 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 11:48:33.889 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 11:53:32.843 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 11:53:33.890 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 11:58:32.842 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 11:58:33.895 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 12:03:32.844 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 12:03:33.893 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 12:08:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 12:08:33.890 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 12:13:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 12:13:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 12:18:32.845 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 12:18:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 12:23:32.849 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 12:23:33.893 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 12:28:32.841 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 12:28:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 12:33:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 12:33:33.895 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 12:38:32.846 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 12:38:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 12:43:32.837 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 12:43:33.894 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 12:48:32.836 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 12:48:33.884 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 12:53:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 12:53:33.892 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 12:58:32.841 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 12:58:33.890 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 13:03:32.846 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 13:03:33.893 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 13:08:32.842 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 13:08:33.892 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 13:13:32.844 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 13:13:33.894 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 13:18:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 13:18:33.881 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 13:23:32.839 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 13:23:33.883 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 13:28:32.843 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 13:28:33.893 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 13:33:32.846 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 13:33:33.895 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 13:38:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 13:38:33.892 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 13:43:32.844 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 13:43:33.896 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 13:48:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 13:48:33.902 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 13:53:32.846 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 13:53:33.895 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 13:58:32.852 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 13:58:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 14:03:32.846 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 14:03:33.890 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 14:08:32.837 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 14:08:33.882 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 14:13:32.837 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 14:13:33.887 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 14:18:32.840 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 14:18:33.889 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 14:23:32.837 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 14:23:33.888 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 14:28:32.840 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 14:28:33.892 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 14:33:32.845 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 14:33:33.902 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 14:38:32.851 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 14:38:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 14:43:32.839 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 14:43:33.889 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 14:48:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 14:48:33.884 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 14:53:32.840 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 14:53:33.887 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 14:58:32.842 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 14:58:33.896 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 15:03:32.845 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 15:03:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 15:08:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 15:08:33.903 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 15:13:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 15:13:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 15:18:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 15:18:33.893 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 15:23:32.844 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 15:23:33.895 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 15:28:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 15:28:33.889 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 15:33:32.843 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 15:33:33.893 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 15:38:32.844 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 15:38:33.895 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 15:43:32.843 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 15:43:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 15:48:32.838 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 15:48:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 15:53:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 15:53:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 15:58:32.845 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 15:58:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 16:03:32.842 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 16:03:33.888 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 16:08:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 16:08:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 16:13:32.850 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 16:13:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 16:18:32.846 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 16:18:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 16:23:32.841 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 16:23:33.894 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 16:28:32.850 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 16:28:33.903 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 16:33:32.850 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 16:33:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 16:38:32.839 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 16:38:33.881 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 16:43:32.839 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 16:43:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 16:48:32.839 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 16:48:33.890 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 16:53:32.843 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 16:53:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 16:58:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 16:58:33.893 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 17:03:32.839 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 17:03:33.882 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 17:08:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 17:08:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 17:13:32.849 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 17:13:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 17:18:32.836 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 17:18:33.887 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 17:23:32.836 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 17:23:33.879 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 17:28:32.841 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 17:28:33.895 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 17:33:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 17:33:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 17:38:32.839 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 17:38:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 17:43:32.846 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 17:43:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 17:48:32.850 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 17:48:33.896 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 17:53:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 17:53:33.903 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 17:58:32.844 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 17:58:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 18:03:32.841 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 18:03:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 18:08:32.839 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 18:08:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 18:13:32.850 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 18:13:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 18:18:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 18:18:33.891 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 18:23:32.848 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 18:23:33.902 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 18:28:32.849 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 18:28:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 18:33:32.846 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 18:33:33.894 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 18:38:32.842 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 18:38:33.886 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 18:43:32.851 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 18:43:33.903 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 18:48:32.847 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 18:48:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 18:53:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 18:53:34.948 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 18:58:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 18:58:34.944 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 19:03:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 19:03:34.952 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 19:08:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 19:08:34.947 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 19:13:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 19:13:34.959 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 19:18:33.908 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 19:18:34.954 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 19:23:33.903 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 19:23:34.951 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 19:28:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 19:28:34.954 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 19:33:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 19:33:34.957 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 19:38:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 19:38:34.953 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 19:43:33.909 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 19:43:34.958 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 19:48:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 19:48:34.948 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 19:53:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 19:53:34.949 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 19:58:33.907 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 19:58:34.958 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 20:03:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 20:03:34.950 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 20:08:33.908 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 20:08:34.953 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 20:13:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 20:13:34.956 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 20:18:33.908 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 20:18:34.957 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 20:23:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 20:23:34.964 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 20:28:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 20:28:34.948 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 20:33:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 20:33:34.945 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 20:38:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 20:38:34.946 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 20:43:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 20:43:34.959 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 20:48:33.906 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 20:48:34.956 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 20:53:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 20:53:34.947 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 20:58:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 20:58:34.956 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 21:03:33.912 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 21:03:34.963 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 21:08:33.906 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 21:08:34.962 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 21:13:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 21:13:34.950 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 21:18:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 21:18:34.942 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 21:23:33.907 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 21:23:34.964 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 21:28:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 21:28:34.946 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 21:33:33.907 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 21:33:34.956 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 21:38:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 21:38:34.959 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 21:43:33.911 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 21:43:34.960 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 21:48:33.908 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 21:48:34.954 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 21:53:33.902 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 21:53:34.959 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 21:58:33.908 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 21:58:34.953 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 22:03:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 22:03:34.952 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 22:08:33.903 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 22:08:34.952 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 22:13:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 22:13:34.960 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 22:18:33.906 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 22:18:34.960 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 22:23:33.903 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 22:23:34.952 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 22:28:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 22:28:34.950 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 22:33:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 22:33:34.948 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 22:38:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 22:38:34.959 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 22:43:33.912 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 22:43:34.962 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 22:48:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 22:48:34.957 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 22:53:33.907 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 22:53:34.959 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 22:58:33.909 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 22:58:34.958 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 23:03:33.907 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 23:03:34.957 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 23:08:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 23:08:34.952 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 23:13:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 23:13:34.944 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 23:18:33.903 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 23:18:34.955 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 23:23:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 23:23:34.961 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 23:28:33.907 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 23:28:34.956 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 23:33:33.913 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 23:33:34.955 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 23:38:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 23:38:34.955 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 23:43:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 23:43:34.955 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 23:48:33.908 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 23:48:34.954 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 23:53:33.911 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 23:53:34.956 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-03 23:58:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-03 23:58:34.941 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
