2025-08-01 13:47:12.030 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Starting AriesGatewayApplication using Java 1.8.0_452 on EM-20230504LGPB with PID 17868 (C:\code2\aries-gateway\etf-gateway\target\classes started by Administrator in C:\code2\aries-gateway)
2025-08-01 13:47:12.037 [main] DEBUG [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Running with Spring Boot v2.4.5, Spring v5.3.6
2025-08-01 13:47:12.038 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - The following profiles are active: dev
2025-08-01 13:47:13.220 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-01 13:47:13.226 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 13:47:13.250 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-08-01 13:47:13.579 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 13:47:13.889 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 81 (http)
2025-08-01 13:47:13.895 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 13:47:13.896 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-08-01 13:47:13.970 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 13:47:13.970 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1904 ms
2025-08-01 13:47:14.195 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.config.MonitorDisableConfig - 基金通监控服务已禁用 - 监控相关功能将不会启动
2025-08-01 13:47:14.195 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.config.MonitorDisableConfig - 已设置系统属性禁用监控功能和 Sentinel Apollo 配置
2025-08-01 13:47:15.396 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.g.c.slot.check.CheckSlotChain - 请求校验链构建完毕
2025-08-01 13:47:15.398 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.g.c.config.SentinelRuleLoader - loadRules sentinelAppName:[aries-gateway-zone--1] dashboard:[null]
2025-08-01 13:47:15.571 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.bridge.common.util.BridgeInit - authKey初始化成功:[IBG-ARIES-GW]
2025-08-01 13:47:15.572 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.g.core.util.WorkdayLoader - 工作日缓存开始加载
2025-08-01 13:47:15.577 [alarm-rule-sync-thread] WARN  [],[],[],[],[],[],[],[],[],[],[] c.t.i.g.core.manager.CommonManager - listGwMonitorDTOs查询异常
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "/null/config/getAllGwMonitor": null; nested exception is org.apache.http.client.ClientProtocolException
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:711)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:631)
	at com.ttfund.ibg.gateway.core.manager.CommonManager.getGwMonitorMap(CommonManager.java:508)
	at com.ttfund.ibg.gateway.core.slot.sentinel.AlarmRuleManager.lambda$initRules$0(AlarmRuleManager.java:38)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.http.client.ClientProtocolException: null
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:187)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:109)
	at com.sofa.alipay.tracer.plugins.rest.interceptor.RestTemplateInterceptor.intercept(RestTemplateInterceptor.java:61)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:93)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:77)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 5 common frames omitted
Caused by: org.apache.http.ProtocolException: Target host is not specified
	at org.apache.http.impl.conn.DefaultRoutePlanner.determineRoute(DefaultRoutePlanner.java:71)
	at org.apache.http.impl.client.InternalHttpClient.determineRoute(InternalHttpClient.java:125)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:184)
	... 17 common frames omitted
2025-08-01 13:47:15.582 [alarm-rule-sync-thread] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.g.c.s.s.AlarmRuleManager - monitor rule sync success
2025-08-01 13:47:15.591 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.g.core.util.WorkdayLoader - 工作日缓存加载完毕.数据量:[980],耗时:[19]ms
2025-08-01 13:47:15.716 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-01 13:47:15.814 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - redis apollo init start
2025-08-01 13:47:16.006 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-08-01 13:47:16.006 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - 资源[ApiCommon-0]初始化成功,当前启用机房[Slave]
2025-08-01 13:47:16.007 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - redis apollo init finish
2025-08-01 13:47:16.389 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2025-08-01 13:47:16.444 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 81 (http) with context path ''
2025-08-01 13:47:17.160 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Started AriesGatewayApplication in 10.341 seconds (JVM running for 11.123)
2025-08-01 13:52:08.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 13:52:09.512 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 13:52:15.585 [alarm-rule-sync-thread] WARN  [],[],[],[],[],[],[],[],[],[],[] c.t.i.g.core.manager.CommonManager - listGwMonitorDTOs查询异常
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "/null/config/getAllGwMonitor": null; nested exception is org.apache.http.client.ClientProtocolException
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:711)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:631)
	at com.ttfund.ibg.gateway.core.manager.CommonManager.getGwMonitorMap(CommonManager.java:508)
	at com.ttfund.ibg.gateway.core.slot.sentinel.AlarmRuleManager.lambda$initRules$0(AlarmRuleManager.java:38)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.http.client.ClientProtocolException: null
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:187)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:109)
	at com.sofa.alipay.tracer.plugins.rest.interceptor.RestTemplateInterceptor.intercept(RestTemplateInterceptor.java:61)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:93)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:77)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 5 common frames omitted
Caused by: org.apache.http.ProtocolException: Target host is not specified
	at org.apache.http.impl.conn.DefaultRoutePlanner.determineRoute(DefaultRoutePlanner.java:71)
	at org.apache.http.impl.client.InternalHttpClient.determineRoute(InternalHttpClient.java:125)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:184)
	... 17 common frames omitted
2025-08-01 13:52:15.588 [alarm-rule-sync-thread] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.g.c.s.s.AlarmRuleManager - monitor rule sync success
2025-08-01 13:55:36.741 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Starting AriesGatewayApplication using Java 1.8.0_452 on EM-20230504LGPB with PID 12552 (C:\code2\aries-gateway\etf-gateway\target\classes started by Administrator in C:\code2\aries-gateway)
2025-08-01 13:55:36.748 [main] DEBUG [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Running with Spring Boot v2.4.5, Spring v5.3.6
2025-08-01 13:55:36.748 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - The following profiles are active: dev
2025-08-01 13:55:37.512 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-01 13:55:37.513 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 13:55:37.535 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-08-01 13:55:37.831 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 13:55:38.155 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 81 (http)
2025-08-01 13:55:38.162 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 13:55:38.162 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-08-01 13:55:38.236 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 13:55:38.237 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1457 ms
2025-08-01 13:55:38.459 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.config.MonitorDisableConfig - 基金通监控服务已禁用 - 监控相关功能将不会启动
2025-08-01 13:55:38.460 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.config.MonitorDisableConfig - 已设置系统属性禁用监控功能和 Sentinel Apollo 配置
2025-08-01 13:55:39.033 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-01 13:55:39.141 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - redis apollo init start
2025-08-01 13:55:39.355 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-08-01 13:55:39.356 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - 资源[ApiCommon-0]初始化成功,当前启用机房[Slave]
2025-08-01 13:55:39.357 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - redis apollo init finish
2025-08-01 13:55:39.605 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2025-08-01 13:55:39.662 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 81 (http) with context path ''
2025-08-01 13:55:40.315 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Started AriesGatewayApplication in 8.738 seconds (JVM running for 9.487)
2025-08-01 13:56:29.644 [SpringContextShutdownHook] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-08-01 13:56:29.657 [SpringContextShutdownHook] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-01 13:56:36.826 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Starting AriesGatewayApplication using Java 1.8.0_452 on EM-20230504LGPB with PID 15564 (C:\code2\aries-gateway\etf-gateway\target\classes started by Administrator in C:\code2\aries-gateway)
2025-08-01 13:56:36.832 [main] DEBUG [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Running with Spring Boot v2.4.5, Spring v5.3.6
2025-08-01 13:56:36.833 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - The following profiles are active: dev
2025-08-01 13:56:37.400 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-01 13:56:37.402 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 13:56:37.422 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-08-01 13:56:37.721 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 13:56:38.058 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 81 (http)
2025-08-01 13:56:38.064 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 13:56:38.065 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-08-01 13:56:38.150 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 13:56:38.151 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1290 ms
2025-08-01 13:56:38.375 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.config.MonitorDisableConfig - 基金通监控服务已禁用 - 监控相关功能将不会启动
2025-08-01 13:56:38.376 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.config.MonitorDisableConfig - 已设置系统属性禁用监控功能和 Sentinel Apollo 配置
2025-08-01 13:56:38.936 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-01 13:56:39.040 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - redis apollo init start
2025-08-01 13:56:39.258 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-08-01 13:56:39.259 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - 资源[ApiCommon-0]初始化成功,当前启用机房[Slave]
2025-08-01 13:56:39.259 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - redis apollo init finish
2025-08-01 13:56:39.503 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2025-08-01 13:56:39.558 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 81 (http) with context path ''
2025-08-01 13:56:40.218 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Started AriesGatewayApplication in 8.568 seconds (JVM running for 9.415)
2025-08-01 14:01:33.285 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 14:01:34.338 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 14:06:33.295 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 14:06:34.339 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 14:11:33.288 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 14:11:34.335 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 14:16:33.285 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 14:16:34.337 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 14:21:33.295 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 14:21:34.339 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 14:26:24.556 [SpringContextShutdownHook] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-08-01 14:26:24.571 [SpringContextShutdownHook] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-01 14:26:32.221 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Starting AriesGatewayApplication using Java 1.8.0_452 on EM-20230504LGPB with PID 9972 (C:\code2\aries-gateway\etf-gateway\target\classes started by Administrator in C:\code2\aries-gateway)
2025-08-01 14:26:32.227 [main] DEBUG [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Running with Spring Boot v2.4.5, Spring v5.3.6
2025-08-01 14:26:32.227 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - The following profiles are active: dev
2025-08-01 14:26:32.818 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-01 14:26:32.820 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 14:26:32.840 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-08-01 14:26:33.161 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 14:26:33.500 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 81 (http)
2025-08-01 14:26:33.507 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 14:26:33.507 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-08-01 14:26:33.580 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 14:26:33.581 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1320 ms
2025-08-01 14:26:33.819 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.config.MonitorDisableConfig - 基金通监控服务已禁用 - 监控相关功能将不会启动
2025-08-01 14:26:33.819 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.config.MonitorDisableConfig - 已设置系统属性禁用监控功能和 Sentinel Apollo 配置
2025-08-01 14:26:34.380 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-01 14:26:34.494 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - redis apollo init start
2025-08-01 14:26:34.708 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-08-01 14:26:34.708 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - 资源[ApiCommon-0]初始化成功,当前启用机房[Slave]
2025-08-01 14:26:34.710 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - redis apollo init finish
2025-08-01 14:26:34.961 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2025-08-01 14:26:35.018 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 81 (http) with context path ''
2025-08-01 14:26:35.673 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Started AriesGatewayApplication in 8.667 seconds (JVM running for 9.546)
2025-08-01 14:31:28.672 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 14:31:29.728 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 14:36:28.675 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 14:36:29.727 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 14:41:28.674 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 14:41:29.727 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 14:46:28.680 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 14:46:29.738 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 14:51:28.679 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 14:51:29.733 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 14:56:28.673 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 14:56:29.722 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 15:01:28.682 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 15:01:29.726 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 15:06:28.694 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 15:06:29.744 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 15:11:28.685 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 15:11:29.739 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 15:16:28.673 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 15:16:29.728 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 15:21:28.671 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 15:21:29.722 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 15:26:28.686 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 15:26:29.742 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 15:31:28.673 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 15:31:29.719 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 15:36:28.673 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 15:36:29.757 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 15:41:28.684 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 15:41:29.740 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 15:46:28.685 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 15:46:29.736 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 15:48:17.894 [SpringContextShutdownHook] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-08-01 15:48:17.907 [SpringContextShutdownHook] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-01 15:48:32.346 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Starting AriesGatewayApplication using Java 1.8.0_452 on EM-20230504LGPB with PID 6192 (C:\code2\aries-gateway\etf-gateway\target\classes started by Administrator in C:\code2\aries-gateway)
2025-08-01 15:48:32.352 [main] DEBUG [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Running with Spring Boot v2.4.5, Spring v5.3.6
2025-08-01 15:48:32.352 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - The following profiles are active: dev
2025-08-01 15:48:33.542 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-01 15:48:33.544 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 15:48:33.567 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-08-01 15:48:34.089 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 15:48:34.394 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 81 (http)
2025-08-01 15:48:34.400 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:48:34.401 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-08-01 15:48:34.871 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:48:34.871 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2490 ms
2025-08-01 15:48:35.086 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.config.MonitorDisableConfig - 基金通监控服务已禁用 - 监控相关功能将不会启动
2025-08-01 15:48:35.087 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.config.MonitorDisableConfig - 已设置系统属性禁用监控功能和 Sentinel Apollo 配置
2025-08-01 15:48:36.265 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-01 15:48:36.371 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - redis apollo init start
2025-08-01 15:48:36.600 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-08-01 15:48:36.600 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - 资源[ApiCommon-0]初始化成功,当前启用机房[Slave]
2025-08-01 15:48:36.602 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - redis apollo init finish
2025-08-01 15:48:36.855 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2025-08-01 15:48:36.914 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 81 (http) with context path ''
2025-08-01 15:48:37.770 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Started AriesGatewayApplication in 10.614 seconds (JVM running for 18.321)
2025-08-01 15:53:28.865 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 15:53:29.920 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 15:58:28.757 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 15:58:29.805 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 16:03:28.758 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 16:03:29.802 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 16:08:28.762 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 16:08:29.821 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 16:13:28.770 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 16:13:29.822 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 16:18:28.886 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 16:18:29.933 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 16:23:28.762 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 16:23:29.805 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 16:28:28.766 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 16:28:29.820 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 16:33:28.762 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 16:33:29.813 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 16:38:28.757 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 16:38:29.803 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 16:43:28.760 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 16:43:29.805 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 16:48:28.756 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 16:48:29.803 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 16:53:28.763 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 16:53:29.808 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 16:58:28.757 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 16:58:29.810 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 17:03:28.756 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 17:03:29.808 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 17:08:28.760 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 17:08:29.817 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 17:13:28.761 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 17:13:29.810 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 17:18:28.765 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 17:18:29.822 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 17:23:28.758 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 17:23:29.816 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 17:28:28.768 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 17:28:29.813 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 17:33:28.771 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 17:33:29.829 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 17:38:28.764 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 17:38:29.808 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 17:43:28.767 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 17:43:29.811 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 17:48:28.767 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 17:48:29.825 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 17:53:28.768 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 17:53:29.889 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 17:58:28.766 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 17:58:29.821 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 18:03:28.757 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 18:03:29.806 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 18:08:28.763 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 18:08:29.820 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 18:13:28.760 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 18:13:29.815 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 18:18:28.763 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 18:18:29.817 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 18:23:28.763 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 18:23:29.812 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 18:28:28.770 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 18:28:29.814 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 18:33:28.772 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 18:33:29.817 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 18:38:28.761 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 18:38:29.807 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 18:43:28.760 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 18:43:29.805 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 18:48:28.766 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 18:48:29.819 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 18:53:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 18:53:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 18:58:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 18:58:31.530 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 19:03:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 19:03:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 19:08:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 19:08:31.539 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 19:13:30.484 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 19:13:31.542 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 19:18:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 19:18:31.532 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 19:23:30.484 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 19:23:31.539 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 19:28:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 19:28:31.510 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 19:33:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 19:33:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 19:38:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 19:38:31.536 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 19:43:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 19:43:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 19:48:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 19:48:31.513 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 19:53:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 19:53:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 19:58:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 19:58:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 20:03:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 20:03:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 20:08:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 20:08:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 20:13:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 20:13:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 20:18:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 20:18:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 20:23:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 20:23:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 20:28:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 20:28:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 20:33:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 20:33:31.536 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 20:38:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 20:38:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 20:43:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 20:43:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 20:48:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 20:48:31.539 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 20:53:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 20:53:31.517 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 20:58:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 20:58:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 21:03:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 21:03:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 21:08:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 21:08:31.538 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 21:13:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 21:13:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 21:18:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 21:18:31.519 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 21:23:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 21:23:31.535 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 21:28:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 21:28:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 21:33:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 21:33:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 21:38:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 21:38:31.534 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 21:43:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 21:43:31.534 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 21:48:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 21:48:31.512 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 21:53:30.483 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 21:53:31.541 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 21:58:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 21:58:31.523 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 22:03:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 22:03:31.533 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 22:08:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 22:08:31.526 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 22:13:30.484 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 22:13:31.541 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 22:18:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 22:18:31.514 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 22:23:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 22:23:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 22:28:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 22:28:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 22:33:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 22:33:31.530 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 22:38:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 22:38:31.534 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 22:43:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 22:43:31.519 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 22:48:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 22:48:31.526 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 22:53:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 22:53:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 22:58:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 22:58:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 23:03:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 23:03:31.537 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 23:08:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 23:08:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 23:13:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 23:13:31.530 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 23:18:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 23:18:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 23:23:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 23:23:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 23:28:30.483 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 23:28:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 23:33:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 23:33:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 23:38:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 23:38:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 23:43:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 23:43:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 23:48:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 23:48:31.526 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 23:53:30.482 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 23:53:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-01 23:58:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-01 23:58:31.534 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
