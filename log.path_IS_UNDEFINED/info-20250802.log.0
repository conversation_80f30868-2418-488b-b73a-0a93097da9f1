2025-08-02 00:03:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 00:03:31.544 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 00:08:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 00:08:31.534 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 00:13:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 00:13:31.536 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 00:18:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 00:18:31.512 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 00:23:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 00:23:31.537 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 00:28:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 00:28:31.536 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 00:33:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 00:33:31.534 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 00:38:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 00:38:31.530 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 00:43:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 00:43:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 00:48:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 00:48:31.517 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 00:53:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 00:53:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 00:58:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 00:58:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 01:03:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 01:03:31.516 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 01:08:30.466 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 01:08:31.510 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 01:13:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 01:13:31.544 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 01:18:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 01:18:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 01:23:30.608 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 01:23:31.796 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 01:28:30.595 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 01:28:31.775 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 01:33:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 01:33:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 01:38:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 01:38:31.536 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 01:43:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 01:43:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 01:48:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 01:48:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 01:53:30.574 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 01:53:31.730 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 01:58:30.610 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 01:58:31.811 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 02:03:30.465 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 02:03:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 02:08:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 02:08:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 02:13:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 02:13:31.513 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 02:18:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 02:18:31.516 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 02:23:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 02:23:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 02:28:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 02:28:31.513 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 02:33:30.608 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 02:33:31.798 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 02:38:30.579 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 02:38:31.751 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 02:43:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 02:43:31.516 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 02:48:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 02:48:31.523 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 02:53:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 02:53:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 02:58:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 02:58:31.535 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 03:03:30.513 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 03:03:31.609 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 03:08:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 03:08:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 03:13:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 03:13:31.514 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 03:18:30.486 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 03:18:31.544 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 03:23:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 03:23:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 03:28:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 03:28:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 03:33:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 03:33:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 03:38:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 03:38:31.535 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 03:43:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 03:43:31.539 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 03:48:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 03:48:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 03:53:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 03:53:31.530 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 03:58:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 03:58:31.533 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 04:03:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 04:03:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 04:08:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 04:08:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 04:13:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 04:13:31.511 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 04:18:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 04:18:31.530 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 04:23:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 04:23:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 04:28:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 04:28:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 04:33:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 04:33:31.534 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 04:38:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 04:38:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 04:43:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 04:43:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 04:48:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 04:48:31.534 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 04:53:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 04:53:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 04:58:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 04:58:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 05:03:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 05:03:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 05:08:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 05:08:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 05:13:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 05:13:31.514 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 05:18:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 05:18:31.526 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 05:23:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 05:23:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 05:28:30.466 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 05:28:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 05:33:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 05:33:31.523 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 05:38:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 05:38:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 05:43:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 05:43:31.519 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 05:48:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 05:48:31.526 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 05:53:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 05:53:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 05:58:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 05:58:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 06:03:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 06:03:31.536 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 06:08:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 06:08:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 06:13:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 06:13:31.535 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 06:18:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 06:18:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 06:23:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 06:23:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 06:28:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 06:28:31.511 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 06:33:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 06:33:31.535 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 06:38:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 06:38:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 06:43:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 06:43:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 06:48:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 06:48:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 06:53:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 06:53:31.533 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 06:58:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 06:58:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 07:03:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 07:03:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 07:08:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 07:08:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 07:13:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 07:13:31.532 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 07:18:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 07:18:31.536 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 07:23:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 07:23:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 07:28:30.466 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 07:28:31.517 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 07:33:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 07:33:31.517 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 07:38:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 07:38:31.514 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 07:43:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 07:43:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 07:48:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 07:48:31.512 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 07:53:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 07:53:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 07:58:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 07:58:31.514 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 08:03:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 08:03:31.512 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 08:08:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 08:08:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 08:13:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 08:13:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 08:18:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 08:18:31.516 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 08:23:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 08:23:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 08:28:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 08:28:31.519 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 08:33:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 08:33:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 08:38:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 08:38:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 08:43:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 08:43:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 08:48:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 08:48:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 08:53:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 08:53:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 08:58:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 08:58:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 09:03:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 09:03:31.511 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 09:08:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 09:08:31.526 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 09:13:30.484 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 09:13:31.538 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 09:18:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 09:18:31.533 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 09:23:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 09:23:31.535 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 09:28:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 09:28:31.541 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 09:33:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 09:33:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 09:38:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 09:38:31.516 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 09:43:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 09:43:31.530 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 09:48:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 09:48:31.519 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 09:53:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 09:53:31.511 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 09:58:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 09:58:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 10:03:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 10:03:31.523 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 10:08:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 10:08:31.532 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 10:13:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 10:13:31.532 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 10:18:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 10:18:31.514 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 10:23:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 10:23:31.509 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 10:28:30.466 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 10:28:31.516 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 10:33:30.490 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 10:33:31.535 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 10:38:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 10:38:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 10:43:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 10:43:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 10:48:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 10:48:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 10:53:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 10:53:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 10:58:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 10:58:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 11:03:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 11:03:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 11:08:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 11:08:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 11:13:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 11:13:31.530 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 11:18:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 11:18:31.538 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 11:23:30.483 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 11:23:31.538 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 11:28:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 11:28:31.526 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 11:33:30.482 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 11:33:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 11:38:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 11:38:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 11:43:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 11:43:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 11:48:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 11:48:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 11:53:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 11:53:31.526 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 11:58:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 11:58:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 12:03:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 12:03:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 12:08:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 12:08:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 12:13:30.484 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 12:13:31.545 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 12:18:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 12:18:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 12:23:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 12:23:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 12:28:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 12:28:31.523 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 12:33:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 12:33:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 12:38:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 12:38:31.510 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 12:43:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 12:43:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 12:48:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 12:48:31.533 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 12:53:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 12:53:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 12:58:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 12:58:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 13:03:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 13:03:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 13:08:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 13:08:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 13:13:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 13:13:31.519 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 13:18:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 13:18:31.516 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 13:23:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 13:23:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 13:28:30.466 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 13:28:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 13:33:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 13:33:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 13:38:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 13:38:31.534 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 13:43:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 13:43:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 13:48:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 13:48:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 13:53:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 13:53:31.535 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 13:58:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 13:58:31.514 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 14:03:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 14:03:31.530 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 14:08:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 14:08:31.526 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 14:13:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 14:13:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 14:18:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 14:18:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 14:23:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 14:23:31.511 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 14:28:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 14:28:31.517 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 14:33:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 14:33:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 14:38:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 14:38:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 14:43:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 14:43:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 14:48:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 14:48:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 14:53:30.482 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 14:53:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 14:58:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 14:58:31.513 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 15:03:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 15:03:31.526 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 15:08:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 15:08:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 15:13:30.835 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 15:13:31.911 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 15:18:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 15:18:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 15:23:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 15:23:31.533 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 15:28:30.642 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 15:28:31.817 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 15:33:30.533 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 15:33:31.684 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 15:38:30.575 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 15:38:31.777 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 15:43:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 15:43:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 15:48:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 15:48:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 15:53:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 15:53:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 15:58:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 15:58:31.516 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 16:03:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 16:03:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 16:08:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 16:08:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 16:13:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 16:13:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 16:18:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 16:18:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 16:23:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 16:23:31.523 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 16:28:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 16:28:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 16:33:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 16:33:31.530 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 16:38:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 16:38:31.517 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 16:43:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 16:43:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 16:48:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 16:48:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 16:53:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 16:53:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 16:58:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 16:58:31.523 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 17:03:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 17:03:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 17:08:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 17:08:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 17:13:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 17:13:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 17:18:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 17:18:31.523 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 17:23:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 17:23:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 17:28:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 17:28:31.526 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 17:33:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 17:33:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 17:38:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 17:38:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 17:43:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 17:43:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 17:48:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 17:48:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 17:53:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 17:53:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 17:58:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 17:58:31.512 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 18:03:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 18:03:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 18:08:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 18:08:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 18:13:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 18:13:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 18:18:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 18:18:31.523 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 18:23:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 18:23:31.521 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 18:28:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 18:28:31.519 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 18:33:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 18:33:31.526 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 18:38:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 18:38:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 18:43:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 18:43:31.523 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 18:48:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 18:48:31.533 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 18:53:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 18:53:31.530 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 18:58:30.473 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 18:58:31.517 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 19:03:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 19:03:31.532 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 19:08:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 19:08:31.516 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 19:13:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 19:13:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 19:18:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 19:18:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 19:23:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 19:23:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 19:28:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 19:28:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 19:33:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 19:33:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 19:38:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 19:38:31.517 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 19:43:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 19:43:31.519 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 19:48:30.582 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 19:48:31.790 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 19:53:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 19:53:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 19:58:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 19:58:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 20:03:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 20:03:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 20:08:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 20:08:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 20:13:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 20:13:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 20:18:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 20:18:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 20:23:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 20:23:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 20:28:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 20:28:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 20:33:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 20:33:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 20:38:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 20:38:31.523 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 20:43:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 20:43:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 20:48:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 20:48:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 20:53:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 20:53:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 20:58:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 20:58:31.530 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 21:03:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 21:03:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 21:08:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 21:08:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 21:13:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 21:13:31.531 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 21:18:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 21:18:31.532 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 21:23:30.466 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 21:23:31.514 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 21:28:30.468 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 21:28:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 21:33:30.466 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 21:33:31.516 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 21:38:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 21:38:31.513 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 21:43:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 21:43:31.515 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 21:48:30.474 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 21:48:31.524 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 21:53:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 21:53:31.523 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 21:58:30.466 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 21:58:31.511 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 22:03:30.484 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 22:03:31.541 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 22:08:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 22:08:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 22:13:30.478 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 22:13:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 22:18:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 22:18:31.528 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 22:23:30.477 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 22:23:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 22:28:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 22:28:31.530 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 22:33:30.481 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 22:33:31.537 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 22:38:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 22:38:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 22:43:30.466 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 22:43:31.523 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 22:48:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 22:48:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 22:53:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 22:53:31.519 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 22:58:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 22:58:31.527 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 23:03:30.471 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 23:03:31.516 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 23:08:30.476 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 23:08:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 23:13:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 23:13:31.520 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 23:18:30.467 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 23:18:31.512 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 23:23:30.489 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 23:23:31.544 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 23:28:30.480 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 23:28:31.538 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 23:33:30.479 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 23:33:31.526 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 23:38:30.475 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 23:38:31.522 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 23:43:30.482 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 23:43:31.536 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 23:48:30.469 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 23:48:31.518 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 23:53:30.470 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 23:53:31.529 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-02 23:58:30.472 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-02 23:58:31.525 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://**************:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
