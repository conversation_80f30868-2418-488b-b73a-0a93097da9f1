2025-08-04 00:03:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 00:03:34.986 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 00:08:33.908 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 00:08:34.953 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 00:13:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 00:13:34.950 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 00:18:33.911 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 00:18:34.961 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 00:23:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 00:23:34.949 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 00:28:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 00:28:34.947 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 00:33:33.906 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 00:33:34.957 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 00:38:33.912 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 00:38:34.965 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 00:43:33.902 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 00:43:34.953 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 00:48:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 00:48:34.955 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 00:53:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 00:53:34.948 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 00:58:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 00:58:34.945 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 01:03:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 01:03:34.944 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 01:08:33.907 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 01:08:34.963 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 01:13:33.903 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 01:13:34.951 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 01:18:33.909 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 01:18:34.963 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 01:23:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 01:23:34.947 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 01:28:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 01:28:34.955 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 01:33:33.908 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 01:33:34.955 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 01:38:33.907 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 01:38:34.953 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 01:43:33.903 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 01:43:34.947 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 01:48:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 01:48:34.954 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 01:53:33.902 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 01:53:34.953 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 01:58:33.903 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 01:58:34.949 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 02:03:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 02:03:34.948 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 02:08:33.902 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 02:08:34.947 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 02:13:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 02:13:34.942 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 02:18:33.909 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 02:18:34.967 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 02:23:33.908 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 02:23:34.958 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 02:28:33.909 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 02:28:34.960 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 02:33:33.909 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 02:33:34.952 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 02:38:33.908 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 02:38:34.956 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 02:43:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 02:43:34.943 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 02:48:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 02:48:34.944 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 02:53:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 02:53:34.949 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 02:58:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 02:58:34.950 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 03:03:33.907 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 03:03:34.957 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 03:08:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 03:08:34.962 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 03:13:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 03:13:34.941 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 03:18:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 03:18:34.944 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 03:23:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 03:23:34.946 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 03:28:33.908 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 03:28:34.965 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 03:33:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 03:33:34.956 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 03:38:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 03:38:34.957 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 03:43:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 03:43:34.963 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 03:48:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 03:48:34.958 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 03:53:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 03:53:34.953 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 03:58:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 03:58:34.951 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 04:03:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 04:03:34.950 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 04:08:33.896 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 04:08:34.943 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 04:13:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 04:13:34.961 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 04:18:33.895 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 04:18:34.949 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 04:23:33.908 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 04:23:34.962 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 04:28:33.906 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 04:28:34.956 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 04:33:33.911 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 04:33:34.963 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 04:38:33.911 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 04:38:34.965 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 04:43:33.906 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 04:43:34.958 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 04:48:33.906 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 04:48:34.959 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 04:53:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 04:53:34.949 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 04:58:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 04:58:34.957 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 05:03:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 05:03:34.952 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 05:08:33.909 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 05:08:34.959 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 05:13:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 05:13:34.952 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 05:18:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 05:18:34.951 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 05:23:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 05:23:34.947 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 05:28:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 05:28:34.958 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 05:33:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 05:33:34.953 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 05:38:33.909 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 05:38:34.955 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 05:43:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 05:43:34.950 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 05:48:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 05:48:34.952 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 05:53:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 05:53:34.961 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 05:58:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 05:58:34.962 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 06:03:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 06:03:34.950 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 06:08:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 06:08:34.947 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 06:13:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 06:13:34.953 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 06:18:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 06:18:34.951 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 06:23:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 06:23:34.950 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 06:28:33.906 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 06:28:34.956 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 06:33:33.909 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 06:33:34.958 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 06:38:33.905 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 06:38:34.955 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 06:43:33.902 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 06:43:34.958 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 06:48:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 06:48:34.962 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 06:53:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 06:53:34.958 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 06:58:33.911 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 06:58:34.956 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 07:03:33.908 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 07:03:34.958 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 07:08:33.904 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 07:08:34.952 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 07:13:33.911 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 07:13:34.960 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 07:18:33.907 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 07:18:34.961 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 07:23:33.899 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 07:23:34.944 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 07:28:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 07:28:34.949 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 07:33:33.903 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 07:33:34.957 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 07:38:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 07:38:34.956 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 07:43:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 07:43:34.954 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 07:48:33.902 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 07:48:34.951 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 07:53:33.895 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 07:53:34.948 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 07:58:33.901 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 07:58:34.947 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 08:03:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 08:03:34.943 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 08:08:33.897 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 08:08:34.948 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 08:13:33.911 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 08:13:34.961 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 08:18:33.898 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 08:18:34.955 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 08:23:33.900 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 08:23:34.950 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 08:28:33.909 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 08:28:34.957 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 08:33:33.910 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.RemoteConfigRepository - Load config failed, will retry in 1 SECONDS. appId: aries-gateway-gold, cluster: default, namespaces: application
2025-08-04 08:33:34.966 [Apollo-RemoteConfigRepository-1] WARN  [],[],[],[],[],[],[],[],[],[],[] c.c.f.a.i.AbstractConfigRepository - Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: aries-gateway-gold, cluster: default, namespace: application, url: http://*************8:8080/configs/aries-gateway-gold/default/application?ip=************ [Cause: [status code: 404] Could not find config for namespace - appId: aries-gateway-gold, cluster: default, namespace: application, please check whether the configs are released in Apollo!]
2025-08-04 08:35:52.891 [SpringContextShutdownHook] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-08-04 08:35:52.946 [SpringContextShutdownHook] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-04 08:42:34.648 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Starting AriesGatewayApplication using Java 1.8.0_452 on EM-20230504LGPB with PID 17872 (C:\code2\aries-gateway\etf-gateway\target\classes started by Administrator in C:\code2\aries-gateway)
2025-08-04 08:42:34.655 [main] DEBUG [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Running with Spring Boot v2.4.5, Spring v5.3.6
2025-08-04 08:42:34.656 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - The following profiles are active: dev
2025-08-04 08:42:35.955 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 08:42:35.957 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 08:42:35.979 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-08-04 08:42:36.303 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 08:42:36.672 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 81 (http)
2025-08-04 08:42:36.679 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-04 08:42:36.679 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-08-04 08:42:36.758 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-04 08:42:36.758 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2065 ms
2025-08-04 08:42:36.988 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.config.MonitorDisableConfig - 基金通监控服务已禁用 - 监控相关功能将不会启动
2025-08-04 08:42:36.988 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.config.MonitorDisableConfig - 已设置系统属性禁用监控功能和 Sentinel Apollo 配置
2025-08-04 08:42:37.597 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-04 08:42:37.706 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - redis apollo init start
2025-08-04 08:42:37.965 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-08-04 08:42:37.966 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - 资源[ApiCommon-0]初始化成功,当前启用机房[Slave]
2025-08-04 08:42:37.967 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.r.c.RedisApolloConfiguration - redis apollo init finish
2025-08-04 08:42:38.234 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2025-08-04 08:42:38.297 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 81 (http) with context path ''
2025-08-04 08:42:38.967 [main] INFO  [],[],[],[],[],[],[],[],[],[],[] c.t.i.a.AriesGatewayApplication - Started AriesGatewayApplication in 5.395 seconds (JVM running for 6.83)
2025-08-04 08:43:11.133 [http-nio-81-exec-2] INFO  [],[],[],[],[],[],[],[],[],[],[] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 08:43:11.133 [http-nio-81-exec-2] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 08:43:11.135 [http-nio-81-exec-2] INFO  [],[],[],[],[],[],[],[],[],[],[] o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
