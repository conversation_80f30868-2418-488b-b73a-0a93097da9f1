<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ttfund.ibg.gateway</groupId>
    <artifactId>gold-common</artifactId>
    <version>1.1.4-SNAPSHOT</version>

    <properties>
        <java.version>1.8</java.version>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <gw.gateway-core.version>2.0.8.22-SNAPSHOT</gw.gateway-core.version>
        <!-- 积存金中台公共类 -->
        <pisces-common.version>1.0.1-SNAPSHOT</pisces-common.version>
    </properties>

    <distributionManagement>
        <repository>
            <id>emfund-releases</id>
            <url>http://nexus.maven.yunwei/repository/3rd_part/</url>
        </repository>
        <snapshotRepository>
            <id>emfund-snapshots</id>
            <url>http://nexus.maven.yunwei/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>emfund-releases</id>
            <name>emfund-releases</name>
            <url>http://nexus.maven.yunwei/repository/3rd_part/</url>

            <releases>
                <enabled>true</enabled>
            </releases>
            <!-- 此项配置，用于打包时依赖最新时间戳的快照版本 -->
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>emfund-snapshots</id>
            <name>emfund-snapshots</name>
            <url>http://nexus.maven.yunwei/repository/maven-snapshots/</url>

            <!-- 此项配置，用于打包时依赖最新时间戳的快照版本 -->
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.22</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

        <dependency>
            <groupId>com.ttfund.ibg.gw</groupId>
            <artifactId>gateway-core</artifactId>
            <version>${gw.gateway-core.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alipay.sofa</groupId>
                    <artifactId>sofa-rpc-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alipay.sofa</groupId>
                    <artifactId>tracer-core</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>mapper-spring-boot-starter</artifactId>
                    <groupId>tk.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-data-mongodb</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ttfund.ibg</groupId>
            <artifactId>pisces-common</artifactId>
            <version>${pisces-common.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ttfund.ibg</groupId>
                    <artifactId>sensitive-mybatis-plugin-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.0</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin> <!-- 打jar包 -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.6</version>
            </plugin>
            <plugin> <!-- 打源码 -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>