package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 开卡预申请 返参
 *
 * <AUTHOR>
 * @date 2025/03/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "开卡预申请 返参")
public class OpenCardPreVO {

    @ApiModelProperty(value = "预申请Id")
    private String preApplyId;

    @ApiModelProperty(value = "需要短信验证，true-表示需要")
    private boolean needSms;

//    @ApiModelProperty(value = "短信Id")
//    private String smsId;
//
//    @ApiModelProperty(value = "接收验证码手机号掩码")
//    private String maskMobileTel;
}
