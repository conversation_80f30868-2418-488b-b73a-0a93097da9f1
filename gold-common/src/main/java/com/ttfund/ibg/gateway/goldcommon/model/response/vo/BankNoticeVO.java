package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 银行通知
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "银行通知")
public class BankNoticeVO {

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 具体内容
     */
    @ApiModelProperty(value = "具体内容")
    private String content;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 等级
     */
    @ApiModelProperty(value = "等级：数字越小优先级越高")
    private Integer level;

    /**
     * Url
     */
    @ApiModelProperty(value = "Url")
    private String linkUrl;

}
