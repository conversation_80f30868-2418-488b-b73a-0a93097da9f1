package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 买卖点
 *
 * <AUTHOR>
 * @date 2025/3/29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "买卖点")
public class BuySellPointVO {

    @ApiModelProperty(value = "点类型：BUY-买点 SELL-卖点")
    private String pointType;

    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;

}
