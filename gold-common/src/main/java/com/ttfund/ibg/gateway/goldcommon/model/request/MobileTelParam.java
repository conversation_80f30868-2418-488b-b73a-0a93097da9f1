package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 获取用户手机号（银行卡手机号/天天基金手机号） 入参
 *
 * <AUTHOR>
 * @date 2025/3/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "获取用户手机号（银行卡手机号/天天基金手机号） 入参")
public class MobileTelParam extends BaseRequest {

    @ApiModelProperty(value = "绑定卡编号（银行卡在基金内部的唯一编号）")
    private String bankAccountNo;

}
