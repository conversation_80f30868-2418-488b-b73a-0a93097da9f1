package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ttfund.ibg.gateway.common.util.TwoRoundUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 资金记录查询 返参
 *
 * <AUTHOR>
 * @date 2025/04/02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(description = "资金记录查询 返参")
public class CashListQueryVO {

    /**
     * EnumBusinType
     */
    @ApiModelProperty(value = "业务类型（cashIn-入金，cashOut-出金）")
    private String businType;

    @ApiModelProperty(value = "交易发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tradeTime;

    @ApiModelProperty(value = "申请金额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal appAmount;

    /**
     * EnumAppState
     */
    @ApiModelProperty(value = "申请状态（1-c成功，2-失败，3-处理中）")
    private String appState;

    @ApiModelProperty(value = "二类户所在行")
    private String bankCode;

    @ApiModelProperty(value = "二类户卡号掩码，例：**** **** **** **** 8866")
    private String bankCardNo;

    @ApiModelProperty(value = "对手方卡号掩码，例：**** **** **** **** 8866")
    private String dfBankCardNo;

    @ApiModelProperty(value = "对手方名称")
    private String dfBankName;

}
