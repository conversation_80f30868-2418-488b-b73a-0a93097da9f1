package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取短信验证码 入参
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "获取短信验证码 入参")
public class SendSmsParam extends BaseRequest {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    @ApiModelProperty(value = "接收验证码手机号(不传默认使用二类卡手机号)")
    private String mobileTel;

    @ApiModelProperty(value = "true-表示newMobileTel是密文，否则做明文处理")
    private Boolean mobileTelEncrypt = Boolean.FALSE;

    @ApiModelProperty(value = "预申请Id", required = true)
    private String preApplyId;

}