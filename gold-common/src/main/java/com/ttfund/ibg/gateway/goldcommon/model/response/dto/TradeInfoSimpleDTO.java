package com.ttfund.ibg.gateway.goldcommon.model.response.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ttfund.ibg.pisces.enums.EnumAppState;
import com.ttfund.ibg.pisces.enums.EnumBusinType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TradeInfoSimpleDTO {

    @ApiModelProperty(value = "交易单号", name = "appSerialNo")
    private String appSerialNo;

    @ApiModelProperty(value = "业务类型", name = "businType")
    private EnumBusinType businType;

    @ApiModelProperty(value = "交易发送时间", name = "appTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date appTime;

    @ApiModelProperty(value = "申请状态", name = "appState")
    private EnumAppState appState;

    @ApiModelProperty(value = "申请金额", name = "appAmount")
    private BigDecimal appAmount;

    @ApiModelProperty(value = "确认金额", name = "cfmAmount")
    private BigDecimal cfmAmount;

    @ApiModelProperty(value = "申请价格", name = "appPrice")
    private BigDecimal appPrice;

    @ApiModelProperty(value = "确认价格", name = "cfmPrice")
    private BigDecimal cfmPrice;

    @ApiModelProperty(value = "申请克重", name = "appVol")
    private BigDecimal appVol;

    @ApiModelProperty(value = "确认克重", name = "cfmVol")
    private BigDecimal cfmVol;

    @ApiModelProperty(value = "手续费", name = "charge")
    private BigDecimal charge;

    @ApiModelProperty(value = "手续费率", name = "chargeRate")
    private BigDecimal chargeRate;

    @ApiModelProperty(value = "银行code", name = "bankCode")
    private String bankCode;

}
