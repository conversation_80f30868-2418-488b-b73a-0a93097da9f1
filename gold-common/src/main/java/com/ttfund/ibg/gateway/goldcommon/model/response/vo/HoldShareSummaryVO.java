package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ttfund.ibg.gateway.common.util.TwoRoundUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 持仓摘要
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "持仓摘要")
public class HoldShareSummaryVO {

    @ApiModelProperty(value = "当前持仓资产金额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal amount;

    @ApiModelProperty(value = "本轮建仓日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate buildDate;

    @ApiModelProperty(value = "本轮建仓天数")
    private Integer buildDays;

    @ApiModelProperty(value = "统计日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate calcDate;

    @ApiModelProperty(value = "预计买入笔数")
    private Integer expectedBuyCount;

    @ApiModelProperty(value = "预计卖出笔数")
    private Integer expectedSellCount;

    @ApiModelProperty(value = "首次交易日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate firstTradeDate;

    @ApiModelProperty(value = "持有成本（平均持仓成本）")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal holdCost;

    @ApiModelProperty(value = "持有收益（平均持仓成本计算）")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal holdProfit;

    @ApiModelProperty(value = "持有收益率（平均持仓成本计算）")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal holdProfitRate;

    @ApiModelProperty(value = "资产更新状态(每日更新 00:30结束)。true-更新中")
    private Boolean onUpdate;

    @ApiModelProperty(value = "持仓成本（摊薄成本）")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal positionCost;

    @ApiModelProperty(value = "持仓收益（摊薄成本计算）")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal positionProfit;

    @ApiModelProperty(value = "持仓收益率（摊薄成本计算）")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal positionProfitRate;

    @ApiModelProperty(value = "今日收益")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal todayProfit;

    @ApiModelProperty(value = "今日收益率")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal todayProfitRate;

    @ApiModelProperty(value = "累计收益")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal totalProfit;

    @ApiModelProperty(value = "累计收益率 不清零")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal totalProfitRate;

    @ApiModelProperty(value = "累计收益 本次建仓 清仓归零")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal totalProfitThisRound;

    @ApiModelProperty(value = "当前持有（克）")
    private BigDecimal weight;

}