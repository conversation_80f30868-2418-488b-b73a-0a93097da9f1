package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 买卖点
 *
 * <AUTHOR>
 * @date 2025/3/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "买卖点 入参")
public class BuySellPointParam extends BaseRequest {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    @ApiModelProperty(value = "开始时间:yyyy-MM-dd", required = true)
    @NotBlank(message = "开始时间不能为空")
    private String beginDate;

    @ApiModelProperty(value = "结束时间：yyyy-MM-dd", required = true)
    @NotBlank(message = "结束时间不能为空")
    private String endDate;

}
