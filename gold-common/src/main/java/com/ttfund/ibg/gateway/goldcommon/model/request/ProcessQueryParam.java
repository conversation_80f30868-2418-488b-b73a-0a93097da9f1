package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 进度查询 入参
 *
 * <AUTHOR>
 * @date 2025/3/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "进度查询 入参")
public class ProcessQueryParam extends BaseRequest {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    /**
     * 流程类型 EnumFollowType
     */
    @ApiModelProperty(value = "openAccount：开户申请\n" +
            "uploadIdCard：上传身份证\n" +
            "cashInSign：入金通道签约申请\n" +
            "goldSign：积存金签约申请\n" +
            "riskTest：风险测评\n" +
            "changeMobileNotUse：变更手机号(旧手机号未使用)\n" +
            "changeMobile：变更手机号(旧手机号依然使用)\n" +
            "changeBindCardWithAsset：非0资产变更绑定卡\n" +
            "changeBindCard：变更绑定卡\n" +
            "cashIn：入金\n" +
            "cashOut：出金\n" +
            "destroyAccount：账户注销\n" +
            "unLock：账户解锁\n" +
            "updateIdCard：更新身份证信息", required = true)
    @NotBlank(message = "流程类型不能为空")
    private String followType;
}
