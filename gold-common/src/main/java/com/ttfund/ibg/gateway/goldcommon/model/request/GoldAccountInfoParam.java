package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 账户状态查询 入参
 *
 * <AUTHOR>
 * @date 2025/3/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "账户状态查询 入参")
public class GoldAccountInfoParam extends BaseRequest {

    @ApiModelProperty(value = "二类户所在行(例：查民生的，传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

}
