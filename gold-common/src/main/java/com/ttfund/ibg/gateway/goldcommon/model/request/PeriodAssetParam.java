package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 区间资产信息 入参
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "区间资产信息 入参")
public class PeriodAssetParam extends BaseRequest {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    @ApiModelProperty(value = "区间（来源为收益日历entity.period字段）。当type为4时，该字段固定上送：0000", required = true)
    @NotBlank(message = "区间（来源为收益日历entity.period字段）不能为空")
    private String period;

    @ApiModelProperty(value = "区间类型：0-DAY,1-WEEK,2-MONTH,3-YEAR 4-从第一笔交易开始统计", required = true)
    @NotBlank(message = "区间类型不能为空")
    private Integer type;

}
