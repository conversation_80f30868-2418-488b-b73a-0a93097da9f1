package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 变更绑定卡预申请
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "变更绑定卡预申请")
public class ChangeBindCardPreVO {

    @ApiModelProperty(value = "是需要短信验证，true-表示需要")
    private Boolean needSms;

    @ApiModelProperty(value = "预申请Id")
    private String preApplyId;

}
