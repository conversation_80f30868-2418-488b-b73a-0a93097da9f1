package com.ttfund.ibg.gateway.goldcommon.service;

import com.ttfund.ibg.gateway.goldcommon.model.request.*;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 积存金：账户信息开户、账户查询等
 *
 * <AUTHOR>
 * @date 2025/3/11
 */
public interface GoldAccountService {

    /**
     * 前置校验（证件/中国税收居民等）
     *
     * @param param 入参
     * @return
     */
    AccountValidVO accountValid(AccountValidParam param);

    /**
     * 二类户账户信息（列表）
     *
     * @param param 入参
     * @return 开通状态
     */
    GoldAccountOverviewListVO getAccountOverviewList(GoldAccountOverviewParam param);

    /**
     * 进度查询
     *
     * @param param
     * @return
     */
    ProcessQueryVO processQuery(ProcessQueryParam param);


    /**
     * 身份证更新预申请
     *
     * @param param
     * @return
     */
    UpdateIdCardPreVO updateIdCardPre(UpdateIdCardPreParam param);

    /**
     * 身份证更新申请
     *
     * @param param
     * @return
     */
    UpdateIdCardVO updateIdCard(UpdateIdCardParam param);

    /**
     * 证件上传
     *
     * @param file
     * @return
     */
    String faceUpload(MultipartFile file);

    /**
     * 开户进度查询
     *
     * @param param
     * @return
     */
    OpenAccountProcessVO openAccountProcessQuery(OpenAccountProcessParam param);

    /**
     * 获取二类卡余额
     *
     * @param param
     * @return
     */
    BalanceQueryVO balanceQuery(BalanceQueryParam param);

    /**
     * 绑定卡校验
     *
     * @param param
     * @return
     */
    BindCardCheckVO bindCardCheck(BindCardCheckParam param);

    /**
     * 变更绑定卡预申请
     *
     * @param param 入参
     * @return 预申请
     */
    ChangeBindCardPreVO changeBindCardPre(ChangeBindCardPreParam param);

    /**
     * 变更绑定卡申请
     *
     * @param param 入参
     * @return 申请
     */
    ChangeBindCardVO changeBindCard(ChangeBindCardParam param);

    /**
     * 变更绑定卡确认
     *
     * @param param 入参
     * @return 确认
     */
    ChangeBindCardConfirmVO changeBindCardConfirm(ChangeBindCardConfirmParam param);

    /**
     * 变更手机号预申请
     *
     * @param param 入参
     * @return 预申请
     */
    ChangeMobilePreVO changeMobilePre(ChangeMobilePreParam param);

    /**
     * 变更手机号申请
     *
     * @param param 入参
     * @return 申请
     */
    ChangeMobileVO changeMobile(ChangeMobileParam param);

    /**
     * 账户解锁预申请
     *
     * @param param 入参
     * @return 预申请
     */
    AccountUnLockPreVO unLockAccountPre(AccountUnLockPreParam param);

    /**
     * 账户解锁申请
     *
     * @param param 入参
     * @return 预申请
     */
    AccountUnLockVO unLockAccount(AccountUnLockParam param);

    /**
     * 账户注销预申请
     *
     * @param param 入参
     * @return 预申请
     */
    DestroyAccountPreVO destroyAccountPre(DestroyAccountPreParam param);

    /**
     * 账户注销申请
     *
     * @param param 入参
     * @return 预申请
     */
    DestroyAccountVO destroyAccount(DestroyAccountParam param);

    /**
     * 区县代码列表(前端自己根据名称模糊匹配)
     *
     * @param param
     * @return
     */
    List<GoldAreaInfoVO> getAreaList(GoldAreaInfoParam param);

    /**
     * 职业代码列表
     *
     * @param param
     * @return
     */
    List<JobInfoVO> getJobList(JobInfoParam param);

    /**
     * 支持的银行列表
     *
     * @param param
     * @return
     */
    BankConfigVO getBankConfig(BankConfigParam param);

    /**
     * 二类户账户信息（明细）
     *
     * @param param 入参
     * @return 开通状态
     */
    GoldAccountInfo getGoldAccountInfo(GoldAccountInfoParam param);

    /**
     * 获取用户手机号（银行卡手机号/天天基金手机号）
     *
     * @param param
     * @return
     */
    MobileTelVO getMobileTel(MobileTelParam param);

    /**
     * 风险测评问题查询
     *
     * @param param 入参
     * @return 风险测评问题
     */
    RiskQuestionQueryVO getRiskQuestion(RiskQuestionQueryParam param);

    /**
     * 提交风险测评
     *
     * @param param 入参
     * @return 提交结果
     */
    RiskResultCommitVO commitRiskResult(RiskResultCommitParam param);

    /**
     * 获取短信验证码
     *
     * @param param
     * @return
     */
    SendSmsVO sendSms(SendSmsParam param);

    /**
     * 开卡预申请
     *
     * @param param
     * @return
     */
    OpenCardPreVO openCardPre(OpenCardPreParam param);

    /**
     * 开卡申请
     *
     * @param param
     * @return
     */
    OpenCardVO openCard(OpenCardParam param);

    /**
     * 入金签约预申请
     *
     * @param param
     * @return
     */
    CashInSignPreVO cashInSignPre(CashInSignPreParam param);

    /**
     * 入金签约申请
     *
     * @param param
     * @return
     */
    CashInSignVO cashInSign(CashInSignParam param);

    /**
     * 积存金签约预申请
     *
     * @param param
     * @return
     */
    GoldSignPreVO goldSignPre(GoldSignPreParam param);

    /**
     * 积存金签约申请
     *
     * @param param
     * @return
     */
    GoldSignVO goldSign(GoldSignParam param);

    /**
     * 适当性问卷提交
     *
     * @param param
     * @return
     */
    AccessResultCommitVO accessResultCommit(AccessResultCommitParam param);

    /**
     * 获取适当性问卷
     *
     * @param param
     * @return
     */
    AccessQuestionVO accessQuestion(AccessQuestionParam param);

    /**
     * 获取风测授权协议书
     *
     * @param param
     * @return
     */
    RiskAuthBookVO getRiskAuthBook(RiskAuthBookParam param);

    /**
     * 风测授权
     *
     * @param param
     * @return
     */
    RiskAuthVO riskAuth(RiskAuthParam param);

    /**
     * 风测前置校验
     *
     * @param param
     * @return
     */
    RiskPreCheckVO riskPreCheck(RiskPreCheckParam param);
}
