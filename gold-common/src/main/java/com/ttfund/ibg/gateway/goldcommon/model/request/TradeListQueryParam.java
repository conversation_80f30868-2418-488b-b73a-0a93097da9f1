package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 交易记录查询 入参
 *
 * <AUTHOR>
 * @date 2025/4/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "交易记录查询 入参")
public class TradeListQueryParam extends BaseRequest {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    @ApiModelProperty(value = "开始时间：yyyy-MM-dd", required = true)
    @NotBlank(message = "开始时间不能为空")
    private String beginDate;

    @ApiModelProperty(value = "结束时间：yyyy-MM-dd", required = true)
    @NotBlank(message = "结束时间不能为空")
    private String endDate;

    @ApiModelProperty(value = "状态列表（1-成功，2-失败，3-处理中）")
    private List<String> appStateList;

    @ApiModelProperty(value = "业务类型列表（cashIn-入金，cashOut-出金，purchase-申购，redeem-赎回到二类卡" +
            "，redeemToBindCard-赎回到绑定卡，giftGold-送金，hlpx-红利派息(派的是黄金)）")
    private List<String> businTypeList;

    @ApiModelProperty(value = "页面大小（最大值为20，超过接口默认使用20）", required = true)
    @NotBlank(message = "页面大小不能为空")
    private Integer pageSize;

    @ApiModelProperty(value = "当前页数", required = true)
    @NotBlank(message = "当前页数不能为空")
    private Integer page;

}
