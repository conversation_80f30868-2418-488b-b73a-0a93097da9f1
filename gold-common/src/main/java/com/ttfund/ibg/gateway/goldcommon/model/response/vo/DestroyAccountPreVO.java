package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 账户注销预申请
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "账户注销预申请")
public class DestroyAccountPreVO {

    @ApiModelProperty(value = "是需要短信验证，true-表示需要")
    private Boolean needSms;

    @ApiModelProperty(value = "预申请Id")
    private String preApplyId;

}
