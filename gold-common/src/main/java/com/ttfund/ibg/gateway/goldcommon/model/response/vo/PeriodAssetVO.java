package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ttfund.ibg.gateway.common.util.FourRoundUtil;
import com.ttfund.ibg.gateway.common.util.TwoRoundUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 区间资产信息
 *
 * <AUTHOR>
 * @date 2025/5/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "持仓摘要")
public class PeriodAssetVO {

    @ApiModelProperty(value = "本期资产")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal amount;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "MM-dd")
    private LocalDate beginDate;

    @ApiModelProperty(value = "买入金额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal buyAmount;

    @ApiModelProperty(value = "买入手续费")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal buyCharge;

    @ApiModelProperty(value = "买入笔数")
    private Integer buyCount;

    @ApiModelProperty(value = "买入克重")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal buyWeight;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "赠金克重（送金）")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal giftWeight;

    @ApiModelProperty(value = "红利派息克重")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal interestWeight;

    @ApiModelProperty(value = "上期资产")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal lastPeriodAsset;

    @ApiModelProperty(value = "上期克重")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal lastPeriodWeight;

    @ApiModelProperty(value = "区间收益")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal periodProfit;

    @ApiModelProperty(value = "区间收益率")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal periodProfitRate;

    @ApiModelProperty(value = "卖出金额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal sellAmount;

    @ApiModelProperty(value = "卖出手续费")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal sellCharge;

    @ApiModelProperty(value = "卖出笔数")
    private Integer sellCount;

    @ApiModelProperty(value = "卖出克重")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal sellWeight;

    @ApiModelProperty(value = "本期克重")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal weight;

    @ApiModelProperty(value = "净流入资产")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal netAsset;

    @ApiModelProperty(value = "净流入克重")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal netWeight;

}