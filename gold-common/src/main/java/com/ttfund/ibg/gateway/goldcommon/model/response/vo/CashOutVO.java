package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ttfund.ibg.gateway.common.util.TwoRoundUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 出金 返参
 *
 * <AUTHOR>
 * @date 2025/03/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "出金 返参")
public class CashOutVO {

    /**
     * EnumBusinType
     */
    @ApiModelProperty(value = "业务类型（cashIn-入金，cashOut-出金）")
    private String businType;

    /**
     * 出金申请状态
     */
    @ApiModelProperty(value = "出金申请状态（1-成功，2-失败，3-处理中，4-部分成功，5-部分失败）")
    private String appState;

    @ApiModelProperty(value = "成功申请金额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal successAppAmount;

    @ApiModelProperty(value = "失败申请金额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal failAppAmount;

    @ApiModelProperty(value = "处理中申请金额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal processAppAmount;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

}
