package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR> <EMAIL>
 * @date 2025/5/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(description = "风测前置校验返参")
public class RiskPreCheckVO {

    @ApiModelProperty(value = "风测状态：0：未测, 1：已测, 2：过期")
    private String riskState;

    @ApiModelProperty(value = "风险等级")
    private String riskLevel;

    @ApiModelProperty(value = "风测过期时间")
    private LocalDateTime riskExpiredTime;

    @ApiModelProperty(value = "true-已授权")
    private Boolean authed;
}
