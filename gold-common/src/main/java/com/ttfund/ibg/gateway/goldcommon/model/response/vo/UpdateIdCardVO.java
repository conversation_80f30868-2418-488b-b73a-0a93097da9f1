package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 身份证更新申请
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "身份证更新申请")
public class UpdateIdCardVO {

    @ApiModelProperty(value = "修改状态：0：未申请 1：成功 2：失败 3：处理中 4：异步申请")
    private String state;

    @ApiModelProperty(value = "失败原因")
    private String failReason;
}
