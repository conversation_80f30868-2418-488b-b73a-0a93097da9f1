package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 交易详情查询
 *
 * <AUTHOR>
 * @date 2025/4/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "交易详情查询")
public class TradeInfoDetailVO extends TradeInfoSimpleVO {

    @ApiModelProperty(value = "二类户卡号（后四位）")
    private String bankCardNo;

    @ApiModelProperty(value = "绑定卡所属银行")
    private String bindBankCode;

    @ApiModelProperty(value = "绑定卡号（后四位）")
    private String bindBankCardNo;

//    @ApiModelProperty("银行卡号描述（银行名称|卡号后四位），例：交通银行|6666")
//    private String bindingBankCardNoDesc;
//
//    @ApiModelProperty(value = "银行卡号（掩码），例：**** **** **** **** 8866")
//    private String maskBankCardNo;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

    @ApiModelProperty(value = "支付方式（0：二类卡账户，1：联动代扣）")
    private String payType;

    @ApiModelProperty(value = "联动赎回(赎回到绑定卡）状态（1：打款成功 2：打款失败 3：打款中)")
    private String linkRedeemState;

    @ApiModelProperty(value = "额外信息")
    private Map<String, Object> extraInfos;

}
