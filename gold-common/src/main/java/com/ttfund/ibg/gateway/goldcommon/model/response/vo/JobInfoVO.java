package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.ttfund.ibg.pisces.entity.resp.JobInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 区县代码
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "区县代码")
public class JobInfoVO {

    @ApiModelProperty(value = "父节点Id")
    private String parentId;

    @ApiModelProperty(value = "职业代码")
    private String jobCode;


    @ApiModelProperty(value = "职业名称")
    private String jobName;

    @ApiModelProperty(value = "子分类")
    private List<JobInfo> childJobList;
}
