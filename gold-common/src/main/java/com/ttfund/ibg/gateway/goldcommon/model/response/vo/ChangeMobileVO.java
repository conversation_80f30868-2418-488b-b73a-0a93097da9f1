package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 变更手机号申请
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "变更手机号申请")
public class ChangeMobileVO {

    @ApiModelProperty(value = "修改状态：0：未申请 1：成功 2：失败 3：处理中 4：异步申请")
    private String changeState;

    @ApiModelProperty(value = "失败原因")
    private String failReason;
}
