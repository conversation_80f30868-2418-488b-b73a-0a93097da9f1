package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 开户进度查询
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "开户进度查询")
public class OpenAccountProcessVO {

    @ApiModelProperty(value = "积存金账户编号（openBankCardInfo为成功时返回）")
    private String goldAccountId;

    @ApiModelProperty(value = "身份证上传信息")
    private ProcessNode idCardUploadInfo;

    @ApiModelProperty(value = "开卡状态信息")
    private ProcessNode openBankCardInfo;

    @Data
    public static class ProcessNode {

        @ApiModelProperty(value = "流程状态(0：未申请 1：成功 2：失败 3：处理中)")
        private String processState;

        @ApiModelProperty(value = "失败原因(processState为2时返回)")
        private String failReason;

    }
}
