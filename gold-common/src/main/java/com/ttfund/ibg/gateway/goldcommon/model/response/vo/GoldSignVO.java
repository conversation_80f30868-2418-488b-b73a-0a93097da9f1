package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 积存金签约申请 返参
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "积存金签约申请 返参")
public class GoldSignVO {

    @ApiModelProperty(value = "签约状态(默认返回处理中,0-未申请,1-成功,2-失败,3-处理中,4-异步申请)")
    private String signState;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

}
