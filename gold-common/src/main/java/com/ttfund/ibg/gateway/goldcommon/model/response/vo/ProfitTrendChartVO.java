package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ttfund.ibg.gateway.common.util.TwoRoundUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 收益趋势图
 *
 * <AUTHOR>
 * @date 2025/3/29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "收益趋势图")
public class ProfitTrendChartVO {

    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;

    @ApiModelProperty(value = "收益")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal profit;

}
