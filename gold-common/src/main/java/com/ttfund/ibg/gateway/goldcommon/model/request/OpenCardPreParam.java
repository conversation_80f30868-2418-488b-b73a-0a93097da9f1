package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequestWithPassword;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 开卡预申请 入参
 *
 * <AUTHOR>
 * @date 2025/03/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "开卡预申请 入参")
public class OpenCardPreParam extends BaseRequestWithPassword {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    @ApiModelProperty(value = "绑定卡号", required = true)
    @NotBlank(message = "绑定卡号不能为空")
    private String bindBankCardNo;

    @ApiModelProperty(value = "绑定卡所属银行", required = true)
    @NotBlank(message = "绑定卡所属银行不能为空")
    private String bindBankCode;

    @ApiModelProperty(value = "区县代码(民生必传)")
    private String districtCode;

    @ApiModelProperty(value = "街道详细地址(民生必传)")
    private String detailAddress;

    @ApiModelProperty(value = "职业代码(民生必传)")
    private String jobCode;

    @ApiModelProperty(value = "职业备注(民生必传且jobCode为82时，该字段必传，且只能传‘父母资助、他人捐赠、个人投资、个体经营’中的一个)")
    private String jobRemark;

    @ApiModelProperty(value = "工作单位(民生必传且jobCode为80、81、82时，该字段只能传‘无’，其他情况不可填无)")
    private String company;

    @ApiModelProperty(value = "二类户预留手机号", required = true)
    @NotBlank(message = "二类户预留手机号不能为空")
    private String mobileTel;

    @ApiModelProperty(value = "true-表示newMobileTel是密文，否则做明文处理", required = true)
    private Boolean mobileTelEncrypt;

    @ApiModelProperty(value = "人脸照片Id", required = true)
    private String faceImageId;

}
