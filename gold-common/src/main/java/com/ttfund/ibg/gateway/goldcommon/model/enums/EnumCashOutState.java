package com.ttfund.ibg.gateway.goldcommon.model.enums;

/**
 * 出金申请状态枚举
 *
 * <AUTHOR>
 * @date 2025/06/16
 */
public enum EnumCashOutState {

    SUCCESS("1", "成功"),
    FAIL("2", "失败"),
    PROCESS("3", "处理中"),
    PART_SUCCESS("4", "部分成功"),
    PART_FAIL("5", "部分失败"),
    ;

    EnumCashOutState(String state, String remark) {
        this.state = state;
        this.remark = remark;
    }

    /**
     * 状态
     */
    private final String state;
    /**
     * 说明
     */
    private final String remark;

    public String getState() {
        return state;
    }

    public String getRemark() {
        return remark;
    }

    public static EnumCashOutState getEnum(String state) {
        for (EnumCashOutState appState : EnumCashOutState.values()) {
            if (appState.getState().equals(state)) {
                return appState;
            }
        }
        return null;
    }


}
