package com.ttfund.ibg.gateway.goldcommon.model.response.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProfitCalendarChartDTO {

    private List<ChartEntity> entityList;

    @Data
    public static class ChartEntity {

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date beginDate;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date endDate;

        private String period;

        private BigDecimal profit;

        private BigDecimal profitRate;

    }
}
