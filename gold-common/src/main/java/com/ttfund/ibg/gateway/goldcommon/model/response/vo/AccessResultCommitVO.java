package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <EMAIL>
 * @date 2025/5/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "适当性问卷提交结果返参")
public class AccessResultCommitVO {

    @ApiModelProperty("适当性测评状态 1:通过，3：处理中")
    private String state;

}
