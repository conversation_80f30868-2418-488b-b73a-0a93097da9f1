package com.ttfund.ibg.gateway.goldcommon.model.response.dto;

import com.ttfund.ibg.pisces.enums.EnumLinkRedeemState;
import com.ttfund.ibg.pisces.enums.EnumPayType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TradeInfoDetailDTO extends TradeInfoSimpleDTO {

    @ApiModelProperty(value = "银行卡号", name = "bankCardNo")
    private String bankCardNo;

    @ApiModelProperty(value = "绑定银行code", name = "bindBankCode")
    private String bindBankCode;

    @ApiModelProperty(value = "绑定银行卡号", name = "bindBankCardNo")
    private String bindBankCardNo;

    @ApiModelProperty(value = "失败原因", name = "failReason")
    private String failReason;

    @ApiModelProperty(value = "支付方式", name = "payType")
    private EnumPayType payType;

    @ApiModelProperty(value = "联动赎回状态", name = "linkRedeemState")
    private EnumLinkRedeemState linkRedeemState;

}
