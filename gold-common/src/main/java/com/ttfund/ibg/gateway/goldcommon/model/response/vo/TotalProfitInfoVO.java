package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ttfund.ibg.gateway.common.util.TwoRoundUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 总收益数据
 *
 * <AUTHOR>
 * @date 2025/5/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "总收益数据")
public class TotalProfitInfoVO {

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate beginDate;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "总收益")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal totalProfit;

    @ApiModelProperty(value = "总收益率")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal totalProfitRate;

}
