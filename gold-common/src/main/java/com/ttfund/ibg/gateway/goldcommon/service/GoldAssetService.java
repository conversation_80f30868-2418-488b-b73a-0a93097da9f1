package com.ttfund.ibg.gateway.goldcommon.service;

import com.ttfund.ibg.gateway.goldcommon.model.request.*;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.*;

import java.util.List;

/**
 * 积存金：资产 收益查询
 *
 * <AUTHOR>
 * @date 2025/3/11
 */
public interface GoldAssetService {

    /**
     * 持仓摘要
     *
     * @param param
     * @return
     */
    HoldShareSummaryVO getHoldShareSummary(HoleShareSummaryParam param);

    /**
     * 总收益/总收益率
     *
     * @param param
     * @return
     */
    TotalProfitInfoVO getTotalProfitInfo(TotalProfitInfoParam param);

    /**
     * 区间资产信息
     *
     * @param param
     * @return
     */
    PeriodAssetVO getPeriodAsset(PeriodAssetParam param);

    /**
     * 收益趋势图
     *
     * @param param
     * @return
     */
    List<ProfitTrendChartVO> getProfitTrendChart(ProfitTrendChartParam param);

    /**
     * 收益日历图
     *
     * @param param
     * @return
     */
    List<ChartEntityVO> getProfitCalendarChart(ProfitCalendarChartParam param);

}
