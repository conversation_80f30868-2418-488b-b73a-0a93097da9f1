package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <EMAIL>
 * @date 2025/5/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "获取适当性问卷入参")
public class AccessQuestionParam extends BaseRequest {

    @ApiModelProperty("二类户所在行(比如开民生的积存金账户，这里传民生的银行code)")
    @NotBlank(message = "银行代码不能为空")
    private String bankCode;
}
