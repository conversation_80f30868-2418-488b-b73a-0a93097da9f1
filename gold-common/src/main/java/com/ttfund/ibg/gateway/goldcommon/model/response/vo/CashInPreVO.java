package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 入金预申请 返参
 *
 * <AUTHOR>
 * @date 2025/03/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "入金预申请 返参")
public class CashInPreVO {

    @ApiModelProperty(value = "预申请Id")
    private String preApplyId;

    @ApiModelProperty(value = "需要短信验证，true-表示需要")
    private boolean needSms;

}
