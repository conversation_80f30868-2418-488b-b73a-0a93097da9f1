package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <EMAIL>
 * @date 2025/5/6
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "风测前置校验入参")
public class RiskPreCheckParam extends BaseRequest {

    @ApiModelProperty("二类户所在行(比如开民生的积存金账户，这里传民生的银行code)")
    private String bankCode;
}
