package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 绑定卡校验 返参
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "绑定卡校验")
public class BindCardCheckVO {

    @ApiModelProperty(value = "true-支持开户")
    private Boolean supportOpenAccount;

    @ApiModelProperty(value = "不支持原因，supportOpenAccount=false时返回")
    private String notSupportReason;

}
