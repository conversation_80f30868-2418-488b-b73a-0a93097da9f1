package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2025/5/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("适当性答案提交入参")
public class AccessResultCommitParam extends BaseRequest {

    @ApiModelProperty("二类户所在行(比如开民生的积存金账户，这里传民生的银行code)")
    @NotBlank(message = "银行代码不能为空")
    private String bankCode;

    @ApiModelProperty("问卷key")
    @NotBlank(message = "问卷key不能为空")
    private String questionnaireKey;

    @ApiModelProperty("答案")
    private List<AnswerInfo> answerInfoList;


    @Data
    public static class AnswerInfo {

        @ApiModelProperty("题目key")
        @NotBlank(message = "题目key不能为空")
        private String questionKey;

        @ApiModelProperty("选项key集合")
        @NotBlank(message = "选项key集合不能为空")
        private Set<String> optKeySet;

    }

}
