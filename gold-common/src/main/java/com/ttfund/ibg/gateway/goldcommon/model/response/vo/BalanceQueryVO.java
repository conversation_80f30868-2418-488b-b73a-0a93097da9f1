package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 二类卡余额
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "二类卡余额")
public class BalanceQueryVO {

    @ApiModelProperty(value = "二类卡余额")
    private BigDecimal balance;

}