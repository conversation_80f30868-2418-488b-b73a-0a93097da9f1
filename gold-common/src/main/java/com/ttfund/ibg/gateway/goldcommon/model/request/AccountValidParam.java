package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 前置校验（证件/中国税收居民等） 入参
 *
 * <AUTHOR>
 * @date 2025/5/28
 */
@Data
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "前置校验（证件/中国税收居民等） 入参")
public class AccountValidParam extends BaseRequest {

}
