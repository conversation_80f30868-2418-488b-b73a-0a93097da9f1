package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 资金记录查询 入参
 *
 * <AUTHOR>
 * @date 2025/04/02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "资金记录查询 入参")
public class CashListQueryParam extends BaseRequest {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    @ApiModelProperty(value = "页面大小（最大值为20，超过接口默认使用20）", required = true)
    private Integer pageSize;

    @ApiModelProperty(value = "当前页数", required = true)
    private Integer page;

    @ApiModelProperty(name = "beginDate", value = "开始时间：yyyy-MM-dd（相隔必须3月以内，如果超过，接口默认查询入参中终止时间到终止时间前3个月的记录）", required = true)
    @NotBlank(message = "开始时间不能为空")
    private String beginDate;

    @ApiModelProperty(name = "endDate", value = "结束时间：yyyy-MM-dd（终止时间不能大于当前日期）", required = true)
    @NotBlank(message = "结束时间不能为空")
    private String endDate;

}
