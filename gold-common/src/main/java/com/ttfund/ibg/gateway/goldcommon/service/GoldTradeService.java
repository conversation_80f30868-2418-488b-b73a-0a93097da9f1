package com.ttfund.ibg.gateway.goldcommon.service;

import com.ttfund.ibg.gateway.goldcommon.model.request.*;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.*;
import com.ttfund.ibg.pisces.entity.PaginationData;

import java.util.List;

/**
 * 积存金：充值 申购 提现 公告
 *
 * <AUTHOR>
 * @date 2025/3/11
 */
public interface GoldTradeService {

    /**
     * 银行通知（系统维护信息等）
     *
     * @param param
     * @return
     */
    List<BankNoticeVO> getBankNotice(BankNoticeParam param);

    /**
     * 黄金市场交易状态
     *
     * @param param
     * @return
     */
    MarketInfoVO getMarketInfo(MarketInfoParam param);

    /**
     * 买卖点
     *
     * @param param
     * @return
     */
    List<BuySellPointVO> getBuySellPoint(BuySellPointParam param);

    /**
     * 入金预申请
     *
     * @param param
     * @return
     */
    CashInPreVO cashInPre(CashInPreParam param);

    /**
     * 入金
     *
     * @param param
     * @return
     */
    CashInVO cashIn(CashInParam param);

    /**
     * 出金预申请
     *
     * @param param
     * @return
     */
    CashOutPreVO cashOutPre(CashOutPreParam param);

    /**
     * 出金
     *
     * @param param
     * @return
     */
    CashOutVO cashOut(CashOutParam param);

    /**
     * 资金记录查询
     *
     * @param param
     * @return
     */
    PaginationData<CashListQueryVO> cashListQuery(CashListQueryParam param);

    /**
     * 交易总览查询
     *
     * @param param
     * @return
     */
    TradeSummaryVO tradeSummaryQuery(TradeSummaryQueryParam param);

    /**
     * 交易记录查询
     *
     * @param param
     * @return
     */
    PaginationData<TradeInfoSimpleVO> tradeListQuery(TradeListQueryParam param);

    /**
     * 交易详情查询
     *
     * @param param
     * @return
     */
    TradeInfoDetailVO tradeDetailQuery(TradeDetailQueryParam param);

    /**
     * 积存金申购
     *
     * @param param
     * @return
     */
    PurchaseVO purchase(PurchaseParam param);

    /**
     * 积存金赎回
     *
     * @param param
     * @return
     */
    RedeemVO redeem(RedeemParam param);

}
