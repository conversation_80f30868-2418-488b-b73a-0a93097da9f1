package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 账户状态查询 入参
 *
 * <AUTHOR>
 * @date 2025/3/12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "账户状态查询 入参")
public class GoldAccountOverviewParam extends BaseRequest {

    @ApiModelProperty(value = "二类户所在行(只查民生的，传民生的银行code，不传查所有的)")
    private String goldBankCode;

    @ApiModelProperty(value = "是否查询二类卡余额：默认False")
    private Boolean isQueryBalance = Boolean.FALSE;

}
