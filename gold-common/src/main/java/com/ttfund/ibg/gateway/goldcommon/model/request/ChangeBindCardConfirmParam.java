package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 变更绑定卡确认 入参
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "变更绑定卡确认 入参")
public class ChangeBindCardConfirmParam extends BaseRequest {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    @ApiModelProperty(value = "确认通过验证码(非必传)")
    private String cfmSmsCode;

    @ApiModelProperty(value = "确认通过验证码")
    private String preApplyId;

}
