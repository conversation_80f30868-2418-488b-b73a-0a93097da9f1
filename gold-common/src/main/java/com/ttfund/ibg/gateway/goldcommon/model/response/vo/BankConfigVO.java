package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ttfund.ibg.gateway.common.util.FourRoundUtil;
import com.ttfund.ibg.gateway.common.util.TwoRoundUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Set;

/**
 * 限额&&费率&支持的银行列表
 *
 * <AUTHOR>
 * @date 2025/4/9
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "支持的银行列表")
public class BankConfigVO {

    @ApiModelProperty(value = "最小申购金额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal minBuyAmount;

    @ApiModelProperty(value = "最小申购克数")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal minBuyWeight;

    @ApiModelProperty(value = "最大申购金额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal maxBuyAmount;

    @ApiModelProperty(value = "最大申购克数")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal maxBuyWeight;

    @ApiModelProperty(value = "申购费率")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal buyRate;

    @ApiModelProperty(value = "赎回费率")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal sellRate;

    @ApiModelProperty(value = "管理费率")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal manageRate;

    @ApiModelProperty(value = "子分类")
    private Set<String> supportBankCode;

    @ApiModelProperty(value = "最小购买追加金额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal buyAmountUnit;

}
