package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ttfund.ibg.gateway.common.util.FourRoundUtil;
import com.ttfund.ibg.gateway.common.util.TwoRoundUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易详情查询
 *
 * <AUTHOR>
 * @date 2025/4/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "交易详情查询")
public class TradeInfoSimpleVO {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，民生的银行code)")
    private String goldBankCode;

    @ApiModelProperty(value = "交易单号")
    private String appSerialNo;

    @ApiModelProperty(value = "业务类型（cashIn-入金，cashOut-出金，purchase-申购，redeem-赎回到二类卡，redeemToBindCard-赎回到绑定卡，giftGold-送金，hlpx-红利派息(派的是黄金)）")
    private String businType;

    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime appTime;

    @ApiModelProperty(value = "申请状态（1-成功，2-失败，3-处理中）")
    private String appState;

    @ApiModelProperty(value = "申请金额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal appAmount;

    @ApiModelProperty(value = "确认金额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal cfmAmount;

    @ApiModelProperty(value = "申请价格")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal appPrice;

    @ApiModelProperty(value = "确认价格")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal cfmPrice;

    @ApiModelProperty(value = "卖出份额(克重)")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal appVol;

    @ApiModelProperty(value = "确认份额(克重)")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal cfmVol;

    @ApiModelProperty(value = "手续费")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal charge;

    @ApiModelProperty(value = "手续费率")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal chargeRate;

}
