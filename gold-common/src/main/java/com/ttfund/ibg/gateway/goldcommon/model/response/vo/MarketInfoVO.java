package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 金价行情通知
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "金价行情通知")
public class MarketInfoVO {

    /**
     * 交易状态（Trading Pause Closed）
     */
    @ApiModelProperty(value = "Trading Pause Closed")
    private String state;

    /**
     * 交易状态对应的文案（交易中/xxx 开盘）
     */
    @ApiModelProperty(value = "交易状态对应的文案（交易中/xxx 开盘）")
    private String stateTip;

    @ApiModelProperty(value = "下次开市时间")
    @JsonFormat(pattern = "MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime nextTradeTime;

}
