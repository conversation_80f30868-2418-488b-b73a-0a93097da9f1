package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 前置校验（证件/中国税收居民等） 入参
 *
 * <AUTHOR>
 * @date 2025/5/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "前置校验（证件/中国税收居民等） 入参")
public class AccountValidVO {

    @ApiModelProperty(value = "类型：-1-校验通过 3-非身份证证件 4-非中国税收居民 5-证件过期 6-证件照片未上传或审核中。")
    private Integer errorType;

}
