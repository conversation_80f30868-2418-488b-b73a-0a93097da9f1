package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequestWithPassword;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 积存金申购 入参
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "积存金申购 入参")
public class PurchaseParam extends BaseRequestWithPassword {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    @ApiModelProperty(value = "申请金额", required = true)
    @NotBlank(message = "申请金额不能为空")
    private BigDecimal appAmount;

    @ApiModelProperty(value = "支付方式（0-二类卡账户，1-联动代扣）", required = true)
    @NotBlank(message = "支付方式不能为空")
    private String payType;

    @ApiModelProperty(value = "金价", required = true)
    @NotBlank(message = "金价不能为空")
    private BigDecimal appPrice;

    @ApiModelProperty(value = "价格Id", required = true)
    @NotBlank(message = "价格Id不能为空")
    private String priceId;

    @ApiModelProperty(value = "产品代码", required = true)
    @NotBlank(message = "产品代码不能为空")
    private String productCode;
}
