package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2025/3/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "风测答案提交入参")
public class RiskResultCommitParam extends BaseRequest {

    @ApiModelProperty(value = "银行代码", name = "bankCode", required = true)
    @NotBlank
    private String bankCode;

    @ApiModelProperty(value = "问卷key", name = "questionnaireKey", required = true)
    @NotBlank
    private String questionnaireKey;

    @ApiModelProperty(value = "答案", name = "answerInfoList", required = true)
    @NotBlank
    private List<AnswerInfo> answerInfoList;

    @ApiModelProperty(value = "true-确认提交（跳过差异化评估)", name = "cfmCommit", required = true)
    @NotBlank
    private Boolean cfmCommit;

    @Data
    public static class AnswerInfo {

        @ApiModelProperty(value = "题目Key", name = "questionKey", required = true)
        @NotBlank
        private String questionKey;

        @ApiModelProperty(value = "选项Key集合", name = "optKeySet", required = true)
        @NotBlank
        private Set<String> optKeySet;

    }
}
