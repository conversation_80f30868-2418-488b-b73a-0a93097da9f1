package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 变更绑定卡预申请 入参
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "变更绑定卡预申请 入参")
public class ChangeBindCardPreParam extends BaseRequest {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    @ApiModelProperty(value = "变更方式(" +
            "openAccount：开户申请 uploadIdCard：上传身份证 cashInSign：入金通道签约申请 " +
            "goldSign：积存金签约申请 riskTest：风险测评 changeMobileNotUse：变更手机号(旧手机号未使用)" +
            "changeMobile：变更手机号(旧手机号依然使用) changeBindCardWithAsset：非0资产变更绑定卡" +
            "changeBindCard：变更绑定卡" +
            ")", required = true)
    @NotBlank(message = "变更方式不能为空")
    private String changeType;

    @ApiModelProperty(value = "手持照片Id")
    private String holdImageId;

    @ApiModelProperty(value = "新绑定卡号", required = true)
    @NotBlank(message = "新绑定卡号不能为空")
    private String newBindBankCardNo;

    @ApiModelProperty(value = "新绑定卡所属银行", required = true)
    @NotBlank(message = "新绑定卡所属银行不能为空")
    private String newBindBankCode;

}
