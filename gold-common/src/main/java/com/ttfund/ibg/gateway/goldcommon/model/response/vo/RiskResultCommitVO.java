package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2025/3/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "风测答案提交返参")
public class RiskResultCommitVO {

    @ApiModelProperty(value = "风测状态：0：未测, 1：已测, 2：过期")
    private String riskState;

    @ApiModelProperty(value = "风险等级")
    private String riskLevel;

    @ApiModelProperty(value = "风测过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime riskExpiredTime;

    @ApiModelProperty(value = "检验结果集（民生：包含'8'，表示'65周岁并且评估结果跨三级'）", name = "statusList")
    private List<String> statusList;

    @ApiModelProperty(value = "存在差异的题目编号（民生是题目的序号，不是questionKey）", name = "statusList")
    private List<String> diffissQuesIdList;
}
