package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ttfund.ibg.gateway.common.util.TwoRoundUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 收益明细
 *
 * <AUTHOR>
 * @date 2025/5/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "收益明细")
public class ChartEntityVO {

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate beginDate;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "区间")
    private String period;

    @ApiModelProperty(value = "收益")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal profit;

    @ApiModelProperty(value = "收益率")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal profitRate;

}
