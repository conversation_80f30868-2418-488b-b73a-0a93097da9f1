package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.SmsInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 账户解锁申请 入参
 *
 * <AUTHOR>
 * @date 2025/3/25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "账户注销申请 入参")
public class AccountUnLockParam extends BaseRequest {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    @ApiModelProperty(value = "预申请Id", required = true)
    @NotBlank(message = "预申请Id不能为空")
    private String preApplyId;

    @ApiModelProperty(value = "验证码信息（无需验证码验证不传）")
    private SmsInfo smsInfo;

}
