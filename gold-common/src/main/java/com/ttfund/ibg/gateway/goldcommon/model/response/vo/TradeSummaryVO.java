package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ttfund.ibg.gateway.common.util.FourRoundUtil;
import com.ttfund.ibg.gateway.common.util.TwoRoundUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 交易总览查询
 *
 * <AUTHOR>
 * @date 2025/4/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "交易总览查询")
public class TradeSummaryVO {

    @ApiModelProperty(value = "买入成功次数")
    private Integer buySuccessTimes;

    @ApiModelProperty(value = "买入总手续费")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal buyTotalCharge;

    @ApiModelProperty(value = "买入总金额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal buyTotalCfmAmount;

    @ApiModelProperty(value = "卖出成功次数")
    private Integer sellSuccessTimes;

    @ApiModelProperty(value = "卖出总金额（不包含手续费）")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal sellTotalCfmAmount;

    @ApiModelProperty(value = "卖出总手续费")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal sellTotalCharge;

    @ApiModelProperty(value = "红利派息次数")
    private Integer hlpxTimes;

    @ApiModelProperty(value = "红利派息克数")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal hlpxTotalCfmVol;

}
