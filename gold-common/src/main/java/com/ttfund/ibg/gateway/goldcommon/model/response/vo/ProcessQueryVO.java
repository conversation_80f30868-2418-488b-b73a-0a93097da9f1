package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 进度查询
 *
 * <AUTHOR>
 * @date 2025/3/29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "进度查询")
public class ProcessQueryVO {

    @ApiModelProperty(value = "预申请Id")
    private String preApplyId;

    @ApiModelProperty(value = "ture-需请求相应确认接口（目前只有民生非0资产变更绑定卡会返回true）")
    private Boolean needCfm;

    @ApiModelProperty(value = "流程状态(0：未申请 1：成功 2：失败 3：处理中)")
    private String processState;

    @ApiModelProperty(value = "失败原因(processState为2时返回)")
    private String failReason;

    @ApiModelProperty(value = "新绑定卡号(followType为绑定卡变更时返回)")
    private String newBindBankCardNo;

    @ApiModelProperty(value = "新绑定卡所属银行(followType为绑定卡变更时返回)")
    private String newBindBankCode;

    @ApiModelProperty(value = "新手机号掩码(followType为手机号变更时返回)")
    private String newMobileMask;

}
