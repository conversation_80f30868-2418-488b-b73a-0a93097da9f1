package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ttfund.ibg.gateway.common.util.FourRoundUtil;
import com.ttfund.ibg.gateway.common.util.TwoRoundUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/05/08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "积存金赎回 返参")
public class RedeemVO {

    @ApiModelProperty(value = "交易单号", name = "appSerialNo")
    private String appSerialNo;

    @ApiModelProperty(value = "银行code", name = "bankCode")
    private String bankCode;

    @ApiModelProperty(value = "交易状态：1-成功，2-失败，3-处理中", name = "appState")
    private String appState;

    @ApiModelProperty(value = "失败原因", name = "failReason")
    private String failReason;

    @ApiModelProperty(value = "申请时间", name = "appTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime appTime;

    @ApiModelProperty(value = "申请克数")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal appVol;

    @ApiModelProperty(value = "确认价格", name = "cfmPrice")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal cfmPrice;

    @ApiModelProperty(value = "确认份额", name = "cfmVol")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal cfmVol;

    @ApiModelProperty(value = "确认金额(支出金额)", name = "cfmAmount")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal cfmAmount;

    @ApiModelProperty(value = "手续费", name = "charge")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal charge;

    @ApiModelProperty(value = "手续费率", name = "chargeRate")
    @JsonSerialize(using = FourRoundUtil.class)
    private BigDecimal chargeRate;

    @ApiModelProperty(value = "交易方式（cashIn-入金，cashOut-出金，purchase-申购，redeem-赎回到二类卡，redeemToBindCard-赎回到绑定卡，giftGold-送金，hlpx-红利派息(派的是黄金)）",
            name = "businType")
    private String businType;

}
