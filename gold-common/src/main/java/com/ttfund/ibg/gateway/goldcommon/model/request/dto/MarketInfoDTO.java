package com.ttfund.ibg.gateway.goldcommon.model.request.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ttfund.ibg.pisces.enums.EnumMarketState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MarketInfoDTO {

    private EnumMarketState state;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date nextTradeTime;
}
