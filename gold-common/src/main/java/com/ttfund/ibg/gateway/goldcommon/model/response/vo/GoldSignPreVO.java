package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 积存金签约预申请 返参
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "积存金签约预申请 返参")
public class GoldSignPreVO {

    @ApiModelProperty(value = "预申请Id")
    private String preApplyId;

    @ApiModelProperty(value = "需要短信验证，true-表示需要")
    private boolean needSms;

    @ApiModelProperty(value = "签约状态：0-未签 1-已签 2-已注销。已签约不用继续调用签约受理)")
    private String goldSignState;

}
