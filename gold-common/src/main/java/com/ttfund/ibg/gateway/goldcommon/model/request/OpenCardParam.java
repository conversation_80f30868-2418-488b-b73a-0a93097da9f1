package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 开卡申请 入参
 *
 * <AUTHOR>
 * @date 2025/03/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "开卡申请 入参")
public class OpenCardParam extends BaseRequest {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    @ApiModelProperty(value = "预申请Id", required = true)
    @NotBlank(message = "预申请Id不能为空")
    private String preApplyId;

    @ApiModelProperty(value = "短信验证码")
    private String smsCode;

    @ApiModelProperty(value = "短信Id")
    private String smsId;

}
