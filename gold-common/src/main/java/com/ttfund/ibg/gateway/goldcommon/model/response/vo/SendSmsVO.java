package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取短信验证码
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "获取短信验证码")
public class SendSmsVO {

    @ApiModelProperty(value = "短信Id")
    private String smsId;

    @ApiModelProperty(value = "接收验证码手机号掩码")
    private String maskMobileTel;

}
