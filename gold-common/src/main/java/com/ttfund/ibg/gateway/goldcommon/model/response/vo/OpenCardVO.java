package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 开卡申请 返参
 *
 * <AUTHOR>
 * @date 2025/03/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "开卡申请 返参")
public class OpenCardVO {

    @ApiModelProperty(value = "失败原因")
    private String failReason;

    /**
     * EnumFollowState
     */
    @ApiModelProperty(value = "开卡结果(默认返回处理中,0-未申请,1-成功,2-失败,3-处理中,4-异步申请)")
    private String openResult;

}
