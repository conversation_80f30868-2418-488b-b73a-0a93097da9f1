package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取用户手机号（银行卡预留手机号/天天基金手机号）
 *
 * <AUTHOR>
 * @date 2025/3/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "用户手机号(明文/密文)")
public class MobileTelVO {

    @ApiModelProperty(value = "用户手机号（掩码，例：188****5336）")
    private String maskMobileTel;

    @ApiModelProperty(value = "手机号密文")
    private String encryptMobileTel;

}
