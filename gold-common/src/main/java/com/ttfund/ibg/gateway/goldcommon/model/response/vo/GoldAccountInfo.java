package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ttfund.ibg.gateway.common.util.TwoRoundUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 账户信息
 *
 * <AUTHOR>
 * @date 2025/3/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "账户信息")
public class GoldAccountInfo {

    @ApiModelProperty(value = "适当性状态：0-未测, 1-已测, 2-过期")
    private String accessState;

    @ApiModelProperty(value = "账户状态：1-有效, 2-注销, 3-身份证过期, 4-账户冻结")
    private String accountState;

    @ApiModelProperty(value = "二类户所在行")
    private String goldBankCode;

    @ApiModelProperty(value = "积存金账户编号")
    private String goldAccountId;

    @ApiModelProperty(value = "二类户卡号掩码，例：**** **** **** **** 8866")
    private String goldBankCardNoMask;

    @ApiModelProperty(value = "二类户卡号")
    private String goldBankCardNo;

    @ApiModelProperty(value = "绑定卡号掩码")
    private String bindBankCardNoMask;

    @ApiModelProperty(value = "绑定卡所属银行")
    private String bindBankCode;

    @ApiModelProperty(value = "二类卡手机号掩码")
    private String maskMobileTel;

    @ApiModelProperty(value = "二类卡手机号密文")
    private String encryptMobileTel;

    @ApiModelProperty(value = "二类卡银行客服电话")
    private String bankServerTel;

    @ApiModelProperty(value = "入金签约状态：0：未签, 1：已签, 2：失败, 3：处理中, 4：不支持签约(不支持通过现有渠道入金)")
    private String cashInSignState;

    @ApiModelProperty(value = "风测状态：0：未测, 1：已测, 2：过期")
    private String riskState;

    @ApiModelProperty(value = "风险等级")
    private String riskLevel;

    @ApiModelProperty(value = "积存金签约状态：0：未签, 1：已签, 2：失败, 3：处理中")
    private String goldSignState;

    @ApiModelProperty(value = "二类卡余额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal balance;

}
