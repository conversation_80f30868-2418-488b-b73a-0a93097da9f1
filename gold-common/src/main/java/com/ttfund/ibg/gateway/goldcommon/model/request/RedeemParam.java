package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequestWithPassword;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/05/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "积存金赎回 入参")
public class RedeemParam extends BaseRequestWithPassword {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    @ApiModelProperty(value = "卖出份额（4位小数）", required = true)
    @NotBlank(message = "卖出份额不能为空")
    private BigDecimal appVol;

    @ApiModelProperty(value = "赎回方式（redeem-赎回到二类卡，redeemToBindCard-赎回到绑定卡）", required = true)
    @NotBlank(message = "赎回方式不能为空")
    private String businType;

    @ApiModelProperty(value = "金价", required = true)
    @NotBlank(message = "金价不能为空")
    private BigDecimal appPrice;

    @ApiModelProperty(value = "价格Id", required = true)
    @NotBlank(message = "价格Id不能为空")
    private String priceId;

    @ApiModelProperty(value = "产品代码", required = true)
    @NotBlank(message = "产品代码不能为空")
    private String productCode;
}
