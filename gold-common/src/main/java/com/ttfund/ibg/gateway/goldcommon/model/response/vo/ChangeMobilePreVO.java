package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 变更手机号预申请
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "变更手机号预申请")
public class ChangeMobilePreVO {

    @ApiModelProperty(value = "预申请Id")
    private String preApplyId;

    @ApiModelProperty(value = "二类卡手机号掩码")
    private String maskMobileTel;

    @ApiModelProperty(value = "二类卡手机号密文")
    private String encryptMobileTel;

    @ApiModelProperty(value = "向旧手机号(二类卡手机号)发验证码，true-表示需要")
    private boolean oldMobileNeedSms;

    @ApiModelProperty(value = "向新手机号发验证码，true-表示需要")
    private boolean newMobileNeedSms;

}
