package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ttfund.ibg.gateway.common.util.TwoRoundUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/03/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "开卡预申请 返参")
public class CashInVO {

    /**
     * EnumBusinType
     */
    @ApiModelProperty(value = "业务类型（cashIn-入金，cashOut-出金）")
    private String businType;

    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime appTime;

    @ApiModelProperty(value = "申请金额")
    @JsonSerialize(using = TwoRoundUtil.class)
    private BigDecimal appAmount;

    /**
     * EnumAppState
     */
    @ApiModelProperty(value = "申请状态（1-成功，2-失败，3-处理中）")
    private String appState;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

    @ApiModelProperty(value = "二类户所在行")
    private String bankCode;

    @ApiModelProperty(value = "二类户卡号")
    private String bankCardNo;

    @ApiModelProperty(value = "绑定卡号")
    private String bindBankCardNo;

    @ApiModelProperty(value = "绑定卡所属银行")
    private String bindBankCode;
}
