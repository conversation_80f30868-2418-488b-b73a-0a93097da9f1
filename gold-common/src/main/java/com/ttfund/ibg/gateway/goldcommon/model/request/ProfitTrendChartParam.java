package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 收益趋势图 入参
 *
 * <AUTHOR>
 * @date 2025/5/12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "收益趋势图 入参")
public class ProfitTrendChartParam extends BaseRequest {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    @ApiModelProperty(value = "开始时间：yyyy-MM-dd", required = true)
    @NotBlank(message = "开始时间不能为空")
    private String beginDate;

    @ApiModelProperty(value = "结束时间：yyyy-MM-dd", required = true)
    @NotBlank(message = "结束时间不能为空")
    private String endDate;

    @ApiModelProperty(value = "最大点数", required = true)
    @NotBlank(message = "最大点数不能为空")
    private Integer maxPointCount;

    @ApiModelProperty(value = "曲线图类型：PROFIT-收益,PROFIT_RATE-收益率,ASSET-资产总额", required = true)
    @NotBlank(message = "曲线图类型不能为空")
    private String type;

}
