package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import com.ttfund.ibg.pisces.entity.resp.AreaInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 区县代码
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "区县代码")
public class GoldAreaInfoVO {

    @ApiModelProperty(value = "父节点Id")
    private String parentId;

    @ApiModelProperty(value = "地区代码")
    private String areaCode;


    @ApiModelProperty(value = "地区名称")
    private String areaName;

    @ApiModelProperty(value = "子分类")
    private List<AreaInfo> childAreaList;

}