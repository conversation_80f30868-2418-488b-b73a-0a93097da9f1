package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2025/3/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "风测问题查询返参")
public class RiskQuestionQueryVO {

    @ApiModelProperty("问卷信息")
    private QuestionnaireInfo questionnaireInfo;

    @Data
    public static class QuestionnaireInfo {

        @ApiModelProperty(value = "问卷key")
        private String questionnaireKey;

        @ApiModelProperty(value = "问卷标题")
        private String questionnaireTitle;

        @ApiModelProperty(value = "问题")
        private List<QuestionInfo> questionInfoList;
    }

    @Data
    public static class QuestionInfo {

        @ApiModelProperty(value = "题目唯一标识")
        private String questionKey;

        @ApiModelProperty(value = "题目")
        private String question;

        @ApiModelProperty(value = "类型  1-单选  2-多选")
        private String optType;

        @ApiModelProperty(value = "选项列表")
        private List<QuesOptInfo> optList;

        @ApiModelProperty(value = "勾稽规则")
        private List<QuesOptJumpInfo> optJumpInfoList;
    }

    @Data
    public static class QuesOptInfo {
        @ApiModelProperty(value = "选项唯一标识")
        private String optKey;

        @ApiModelProperty(value = "选项内容")
        private String optContent;

        @ApiModelProperty(value = "ture-已选中")
        private Boolean selected;

        @ApiModelProperty(value = "true-可选，false-不可选")
        private Boolean isOptional;
    }

    @Data
    public static class QuesOptJumpInfo {
        @ApiModelProperty(value = "选择的optKey")
        private String optKey;

        @ApiModelProperty(value = "勾稽的题目Key")
        private String linkQuestionKey;

        @ApiModelProperty(value = "勾稽的题目的选项Key")
        private String linkQuesOptKey;
    }
}
