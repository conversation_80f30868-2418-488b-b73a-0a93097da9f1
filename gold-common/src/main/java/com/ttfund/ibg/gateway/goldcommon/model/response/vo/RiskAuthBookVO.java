package com.ttfund.ibg.gateway.goldcommon.model.response.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <EMAIL>
 * @date 2025/5/6
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "获取风测授权协议书返参")
public class RiskAuthBookVO {

    @ApiModelProperty(value = "文件Url", name = "fileUrl")
    private String fileUrl;

    @ApiModelProperty(value = "文件Id", name = "fileId")
    private String fileId;
}
