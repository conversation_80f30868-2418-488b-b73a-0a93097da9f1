package com.ttfund.ibg.gateway.goldcommon.model.request;

import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequestWithPassword;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 入金签约预申请 入参
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "入金签约预申请 入参")
public class CashInSignPreParam extends BaseRequestWithPassword {

    @ApiModelProperty(value = "二类户所在行(比如开民生的积存金账户，这里传民生的银行code)", required = true)
    @NotBlank(message = "二类户所在行不能为空")
    private String goldBankCode;

    @ApiModelProperty(value = "入金通道（不传使用从银行查询的最优通道）")
    private String cashInChannel;

}
