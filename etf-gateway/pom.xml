<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.4.5</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.ttfund.ibg.gateway</groupId>
    <artifactId>etf-gateway</artifactId>
    <version>1.1.3-SNAPSHOT</version>
    <name>etf-gateway</name>
    <description>互联网手机接口 etf</description>

    <properties>
        <java.version>1.8</java.version>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <gold-common.version>1.1.4-SNAPSHOT</gold-common.version>
        <brigde-common.version>1.3.1</brigde-common.version>
        <aries-core.version>1.1.1</aries-core.version>
    </properties>

    <repositories>
        <repository>
            <id>emfund-releases</id>
            <name>emfund-releases</name>
            <url>http://nexus.maven.yunwei/repository/maven-snapshots/</url>

            <releases>
                <enabled>true</enabled>
            </releases>
            <!-- 此项配置，用于打包时依赖最新时间戳的快照版本 -->
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </repository>

    </repositories>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.4.2.Final</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.4.2.Final</version>
        </dependency>

        <dependency>
            <groupId>com.ttfund.ibg.gateway</groupId>
            <artifactId>gold-common</artifactId>
            <version>${gold-common.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-data-mongodb</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-rpc-all</artifactId>
            <version>5.5.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alipay.sofa</groupId>
                    <artifactId>tracer-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>4.9</version>
        </dependency>

        <!-- SkyWalking日志追踪 -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>8.16.0</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.6.2</version>
        </dependency>

        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-tracer-springmvc-plugin</artifactId>
            <version>3.1.1</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.alipay.sofa/sofa-tracer-httpclient-plugin -->
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-tracer-httpclient-plugin</artifactId>
            <version>3.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-tracer-resttmplate-plugin</artifactId>
            <version>3.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>tracer-extensions</artifactId>
            <version>3.0.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.10</version>
        </dependency>


        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.stuxuhai</groupId>
            <artifactId>jpinyin</artifactId>
            <version>1.1.8</version>
        </dependency>

        <dependency>
            <groupId>org.ansj</groupId>
            <artifactId>ansj_seg</artifactId>
            <version>5.1.1</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.danielwegener</groupId>
            <artifactId>logback-kafka-appender</artifactId>
            <version>0.2.0-RC2</version>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>6.6</version>
        </dependency>

        <dependency>
            <groupId>com.ttfund.ibg</groupId>
            <artifactId>redis-apollo-starter</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>3.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.ttfund.ibg</groupId>
            <artifactId>bridge-common</artifactId>
            <version>${brigde-common.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ttfund.ibg</groupId>
            <artifactId>aries-core-interface</artifactId>
            <version>${aries-core.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <build>
        <finalName>aries-gateway</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <spring_profile>dev</spring_profile>
            </properties>
        </profile>

        <profile>
            <id>test</id>
            <properties>
                <spring_profile>test</spring_profile>
            </properties>
        </profile>

        <profile>
            <id>uat</id>
            <properties>
                <spring_profile>uat</spring_profile>
            </properties>
        </profile>

        <profile>
            <id>beta</id>
            <properties>
                <spring_profile>beta</spring_profile>
            </properties>
        </profile>

        <profile>
            <id>prod</id>
            <properties>
                <spring_profile>prod</spring_profile>
            </properties>
        </profile>
    </profiles>

</project>
