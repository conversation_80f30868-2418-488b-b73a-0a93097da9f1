# 脚本说明：启动同层目录下的jar文件
# 其他说明：jar文件必须位于startup.sh目录的同一层目录。

# spring环境 内测赋值uat 生产赋值prod 税延环境赋值test 公测赋值beta
PROFILE="uat"
jar_dir=$(
  cd $(dirname $0) || exit 1
  pwd
)
echo "jar path: ${jar_dir}"
jar_file="${jar_dir}/aries-gateway.jar"
echo "jar file: ${jar_file}"

jvm_log_dir="${jar_dir}/jvm-logs"
if [ ! -d "${jvm_log_dir}" ]; then
  mkdir ${jvm_log_dir}
  echo "mkdir ${jvm_log_dir}"
fi

gc_log_dir="${jvm_log_dir}/gc"
if [ ! -d "${gc_log_dir}" ]; then
  mkdir ${gc_log_dir}
  echo "mkdir ${gc_log_dir}"
fi

heap_log_dir="${jvm_log_dir}/heap"
if [ ! -d "${heap_log_dir}" ]; then
  mkdir ${heap_log_dir}
  echo "mkdir ${heap_log_dir}"
fi

# JVM参数 无需修改
JAVA_OPT="${JAVA_OPT} -server"
JAVA_OPT="${JAVA_OPT} -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintTenuringDistribution -XX:+PrintHeapAtGC -XX:+PrintGCApplicationStoppedTime"
JAVA_OPT="${JAVA_OPT} -Xloggc:${gc_log_dir}/gc.log -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=50M"
JAVA_OPT="${JAVA_OPT} -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${heap_log_dir}/heap.hprof"

if [ -f "${jar_file}" ]; then
  nohup java ${JAVA_OPT} -jar ${jar_file} --spring.config.location=${jar_dir}/config/ --spring.profiles.active=${PROFILE} >/dev/null 2>&1 &
  echo "${jar_file} is starting..."
  echo "jvm参数: ${JAVA_OPT}"
  exit 0
else
  echo "${jar_file}文件不存在!"
  exit 1
fi
