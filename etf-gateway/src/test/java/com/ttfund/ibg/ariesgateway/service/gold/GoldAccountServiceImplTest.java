package com.ttfund.ibg.ariesgateway.service.gold;

import com.ttfund.ibg.gateway.goldcommon.model.request.*;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.*;
import com.ttfund.ibg.gateway.goldcommon.service.GoldAccountService;
import com.ttfund.ibg.pisces.enums.EnumBusinType;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@Slf4j
@SpringBootTest
@DisplayName("积存金账户")
class GoldAccountServiceImplTest {

    @Resource
    GoldAccountService accountService;

    @Test
    void accountValid() {
        Integer errorType = Integer.valueOf("-1");
        if (Integer.valueOf(0).compareTo(errorType) > 0) {
            log.info("{}", Boolean.TRUE);
        } else {
            log.info("{}", Boolean.FALSE);
        }
    }

    @Test
    void getAccountOverviewList() {
        GoldAccountOverviewParam param = new GoldAccountOverviewParam();
        param.setGoldBankCode("014");
        param.setInvestorId("8ad4dd925e2548219c8abde19afc8f21");
        GoldAccountOverviewListVO accountInfo = accountService.getAccountOverviewList(param);
        log.info("getAccountInfo{}", accountInfo);
        assertNotNull(accountInfo);
    }

    @Test
    void bindCardCheck() {
        EnumBusinType test = EnumBusinType.getEnum("test");
    }

    @Test
    void balanceQuery() {
    }

    @Test
    void getAreaList() {
    }

    @Test
    void getJobList() {
        JobInfoParam param = new JobInfoParam();
        param.setGoldBankCode("014");
        List<JobInfoVO> jobList = accountService.getJobList(param);
        log.info("result:{}", jobList);
    }

    @Test
    void testGetAccountOverviewList() {
        GoldAccountOverviewParam param = new GoldAccountOverviewParam();
        param.setGoldBankCode("014");
        param.setInvestorId("8ad4dd925e2548219c8abde19afc8f21");
        GoldAccountOverviewListVO accountOverviewList = accountService.getAccountOverviewList(param);
        log.info("result:{}", accountOverviewList);
    }

    @Test
    void getGoldAccountInfo() {
    }

    @Test
    void getRiskQuestion() {
    }

    @Test
    void commitRiskResult() {
    }

    @Test
    void testBindCardCheck() {
    }

    @Test
    void changeBindCardPre() {
    }

    @Test
    void changeBindCard() {
    }

    @Test
    void changeBindCardConfirm() {
    }

    @Test
    void changeMobilePre() {
    }

    @Test
    void changeMobile() {
    }

    @Test
    void unLockAccountPre() {
    }

    @Test
    void unLockAccount() {
    }

    @Test
    void destroyAccountPre() {
    }

    @Test
    void destroyAccount() {
    }

    @Test
    void testBalanceQuery() {
    }

    @Test
    void testGetAreaList() {
    }

    @Test
    void testGetJobList() {
    }

    @Test
    void sendSms() {
    }

    @Test
    void updateIdCardPre() {
    }

    @Test
    void updateIdCard() {
    }

    @Test
    void testGetGoldAccountInfo() {
    }

    @Test
    void getMobileTel() {
    }

    @Test
    void testGetRiskQuestion() {
    }

    @Test
    void testCommitRiskResult() {
    }

    @Test
    void testBindCardCheck1() {
    }

    @Test
    void testChangeBindCardPre() {
    }

    @Test
    void testChangeBindCard() {
    }

    @Test
    void testChangeBindCardConfirm() {
    }

    @Test
    void testChangeMobilePre() {
    }

    @Test
    void testChangeMobile() {
    }

    @Test
    void testUnLockAccountPre() {
    }

    @Test
    void testUnLockAccount() {
    }

    @Test
    void testDestroyAccountPre() {
    }

    @Test
    void testDestroyAccount() {
    }

    @Test
    void testSendSms() {
    }

    @Test
    void processQuery() {
        ProcessQueryParam param = new ProcessQueryParam();
        param.setGoldBankCode("014");
        param.setInvestorId("8ad4dd925e2548219c8abde19afc8f21");
        param.setFollowType("cashIn");
        ProcessQueryVO processQueryVO = accountService.processQuery(param);
        log.info("result:{}", processQueryVO);

    }

    @Test
    void testUpdateIdCardPre() {
        UpdateIdCardPreParam param = new UpdateIdCardPreParam();
        param.setGoldBankCode("014");
        param.setInvestorId("8ad4dd925e2548219c8abde19afc8f21");
        UpdateIdCardPreVO updateIdCardPreVO = accountService.updateIdCardPre(param);
        log.info("result:{}", updateIdCardPreVO);
    }

    @Test
    void testUpdateIdCard() {
    }

    @Test
    void faceUpload() {
    }

    @Test
    void testGetMobileTel() {
    }

    @Test
    void getBankConfig() {
        BankConfigParam param = new BankConfigParam();
        param.setGoldBankCode("014");
        BankConfigVO bankConfig = accountService.getBankConfig(param);
        log.info("result:{}", bankConfig);
    }

    @Test
    void openCardPre() {
    }

    @Test
    void openCard() {
    }

    @Test
    void cashInSignPre() {
    }

    @Test
    void cashInSign() {
    }

    @Test
    void goldSignPre() {
    }

    @Test
    void goldSign() {
    }

}