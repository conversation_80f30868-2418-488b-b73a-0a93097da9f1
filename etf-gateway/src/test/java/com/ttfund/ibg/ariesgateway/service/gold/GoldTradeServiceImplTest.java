package com.ttfund.ibg.ariesgateway.service.gold;

import com.ttfund.ibg.gateway.goldcommon.model.request.*;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.*;
import com.ttfund.ibg.gateway.goldcommon.service.GoldTradeService;
import com.ttfund.ibg.pisces.entity.PaginationData;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DisplayName("积存金交易")
class GoldTradeServiceImplTest {

    @Resource
    GoldTradeService goldTradeService;

    @Test
    void getBankNotice() {
        BankNoticeParam param = new BankNoticeParam();
        param.setGoldBankCode("014");
        List<BankNoticeVO> bankNotice = goldTradeService.getBankNotice(param);
        log.info("result:{}:", bankNotice);
    }

    @Test
    void getMarketInfo() {
        MarketInfoParam param = new MarketInfoParam();
        param.setGoldBankCode("014");
        MarketInfoVO marketInfo = goldTradeService.getMarketInfo(param);
        log.info("result:{}", marketInfo);
    }

    @Test
    void tradeListQuery() {
    }

    @Test
    void tradeDetailQuery() {
    }

    @Test
    void testGetBankNotice() {
    }

    @Test
    void testGetMarketInfo() {
    }

    @Test
    void getBuySellPoint() {
        BuySellPointParam param = new BuySellPointParam();
        param.setGoldBankCode("014");
        param.setInvestorId("5ce1cc8b410a4344873e1fd45ca91da6");
        param.setBeginDate(LocalDate.now().minusDays(16).toString());
        param.setEndDate(LocalDate.now().toString());
        List<BuySellPointVO> res = goldTradeService.getBuySellPoint(param);
        log.info("result:{}:", res);
    }

    @Test
    void cashInPre() {
    }

    @Test
    void cashIn() {
    }

    @Test
    void cashOutPre() {
    }

    @Test
    void cashOut() {
    }

    @Test
    void cashListQuery() {
    }

    @Test
    void tradeSummaryQuery() {
        TradeSummaryQueryParam param = new TradeSummaryQueryParam();
        param.setGoldBankCode("014");
        param.setInvestorId("5ce1cc8b410a4344873e1fd45ca91da6");
        param.setBeginDate(LocalDate.now().minusDays(16).toString());
        param.setEndDate(LocalDate.now().toString());
        TradeSummaryVO tradeSummaryVO = goldTradeService.tradeSummaryQuery(param);
        log.info("result:{}:", tradeSummaryVO);
    }

    @Test
    void testTradeListQuery() {
        TradeListQueryParam param = new TradeListQueryParam();
        param.setGoldBankCode("014");
        param.setInvestorId("5ce1cc8b410a4344873e1fd45ca91da6");
        param.setBeginDate(LocalDate.now().minusDays(16).toString());
        param.setEndDate(LocalDate.now().toString());
        param.setPageSize(10);
        param.setPage(1);
        PaginationData<TradeInfoSimpleVO> TradeInfoSimpleVO = goldTradeService.tradeListQuery(param);
        log.info("result:{}:", TradeInfoSimpleVO);
    }

    @Test
    void testTradeDetailQuery() {
        TradeDetailQueryParam param = new TradeDetailQueryParam();
        param.setGoldBankCode("014");
        param.setInvestorId("5ce1cc8b410a4344873e1fd45ca91da6");
        param.setAppSerialNo("43203a151eeb4255a53783b35254cb23");
        TradeInfoDetailVO tradeInfoDetailVO = goldTradeService.tradeDetailQuery(param);
        log.info("result:{}:", tradeInfoDetailVO);
    }
}