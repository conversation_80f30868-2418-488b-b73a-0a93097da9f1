package com.ttfund.ibg.ariesgateway.service.gold;

import com.ttfund.ibg.gateway.goldcommon.model.request.HoleShareSummaryParam;
import com.ttfund.ibg.gateway.goldcommon.model.request.PeriodAssetParam;
import com.ttfund.ibg.gateway.goldcommon.model.request.ProfitTrendChartParam;
import com.ttfund.ibg.gateway.goldcommon.model.request.TotalProfitInfoParam;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.HoldShareSummaryVO;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.PeriodAssetVO;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.ProfitTrendChartVO;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.TotalProfitInfoVO;
import com.ttfund.ibg.gateway.goldcommon.service.GoldAssetService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@Slf4j
@SpringBootTest
@DisplayName("积存金账交易")
class GoldAssetServiceImplTest {

    @Resource
    GoldAssetService goldAssetService;

    @Test
    void getHoldShareSummary() {
        HoleShareSummaryParam param = new HoleShareSummaryParam();
        param.setInvestorId("8ad4dd925e2548219c8abde19afc8f21");
        param.setGoldBankCode("014");
        HoldShareSummaryVO res = goldAssetService.getHoldShareSummary((param));

        log.info("result:{}", res);
        assertNotNull(res);

    }

    @Test
    void getTotalProfitInfo() {
        TotalProfitInfoParam param = new TotalProfitInfoParam();
        param.setInvestorId("8ad4dd925e2548219c8abde19afc8f21");
        param.setGoldBankCode("014");
        TotalProfitInfoVO res = goldAssetService.getTotalProfitInfo((param));

        log.info("result:{}", res);
        assertNotNull(res);
    }

    @Test
    void getPeriodAsset() {
        PeriodAssetParam param = new PeriodAssetParam();
        param.setInvestorId("8ad4dd925e2548219c8abde19afc8f21");
        param.setGoldBankCode("014");
        param.setPeriod("1");
        param.setType(1);
        PeriodAssetVO res = goldAssetService.getPeriodAsset((param));

        log.info("result:{}", res);
        assertNotNull(res);
    }

    @Test
    void getProfitTrendChart() {
        ProfitTrendChartParam param = new ProfitTrendChartParam();
        param.setInvestorId("8ad4dd925e2548219c8abde19afc8f21");
        param.setGoldBankCode("014");
        param.setBeginDate("2020-05-26");
        param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd") ));
        param.setMaxPointCount(168);
        param.setType("PROFIT");
        List<ProfitTrendChartVO> res = goldAssetService.getProfitTrendChart((param));

        log.info("result:{}", res);
        assertNotNull(res);
    }

    @Test
    void getProfitCalendarChart() {
    }
}