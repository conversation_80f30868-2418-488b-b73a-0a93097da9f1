package com.ttfund.ibg.ariesgateway.controller.gold;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.ttfund.ibg.gateway.common.annotation.NotBlank;
import com.ttfund.ibg.gateway.common.model.param.BaseRequest;
import com.ttfund.ibg.gateway.common.model.response.IbgResultInfo;
import com.ttfund.ibg.gateway.common.model.response.ResultInfo;
import com.ttfund.ibg.gateway.goldcommon.model.request.*;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.*;
import com.ttfund.ibg.gateway.goldcommon.service.GoldAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 积存金：账户信息开户、账户查询等
 *
 * <AUTHOR>
 * @date 2025/3/12
 */
@Api(value = "积存金：账户信息开户、账户查询controller", tags = "积存金：账户信息开户、账户查询controller")
@Slf4j
@RestController
@RequestMapping("/gold/account")
public class GoldAccountController {

    @Resource
    GoldAccountService goldAccountService;

    @ApiOperation(value = "前置校验（证件/中国税收居民等）")
    @PostMapping(value = "/accountValid")
    @SentinelResource("aries-gw#goldAccountValid")
    public IbgResultInfo<AccountValidVO> accountValid(@RequestBody AccountValidParam param) {
        return new IbgResultInfo<AccountValidVO>().succeed(goldAccountService.accountValid(param));
    }

    @ApiOperation(value = "进度查询", notes = "支持查询所有流程的状态。在进行绑定卡变更、入金签约、风险测评、积存金签约、手机号变更、注销、账户解锁、身份证更新之前，可以调用此接口，获取用户是否已发起对应操作，以及操作的结果和失败原因，根据结果判断是否要重新发起流程，还是等待中台同步操作结果")
    @PostMapping(value = "/processQuery")
    @SentinelResource("aries-gw#goldProcessQuery")
    public IbgResultInfo<ProcessQueryVO> processQuery(@RequestBody ProcessQueryParam param) {
        return new IbgResultInfo<ProcessQueryVO>().succeed(goldAccountService.processQuery(param));
    }

    @ApiOperation(value = "二类户账户信息（列表）")
    @PostMapping(value = "/getAccountOverviewList")
    @SentinelResource("aries-gw#goldAccountOverviewList")
    public IbgResultInfo<GoldAccountOverviewListVO> getAccountOverviewList(@RequestBody GoldAccountOverviewParam param) {
        return new IbgResultInfo<GoldAccountOverviewListVO>().succeed(goldAccountService.getAccountOverviewList(param));
    }

    @ApiOperation(value = "获取用户手机号（银行卡预留手机号/天天基金手机号）")
    @PostMapping("/getMobileTel")
    @SentinelResource("gw#goldMobileTel")
    public ResultInfo<MobileTelVO> getMobileTel(@RequestBody @NotBlank MobileTelParam param) {
        return new ResultInfo<MobileTelVO>().succeed(goldAccountService.getMobileTel(param));
    }

    @ApiOperation(value = "身份证更新预申请")
    @PostMapping(value = "/updateIdCardPre")
    @SentinelResource("aries-gw#goldUpdateIdCardPre")
    public IbgResultInfo<UpdateIdCardPreVO> updateIdCardPre(@RequestBody UpdateIdCardPreParam param) {
        return new IbgResultInfo<UpdateIdCardPreVO>().succeed(goldAccountService.updateIdCardPre(param));
    }

    @ApiOperation(value = "身份证更新申请")
    @PostMapping(value = "/updateIdCard")
    @SentinelResource("aries-gw#goldUpdateIdCard")
    public IbgResultInfo<UpdateIdCardVO> updateIdCard(@RequestBody UpdateIdCardParam param) {
        return new IbgResultInfo<UpdateIdCardVO>().succeed(goldAccountService.updateIdCard(param));
    }

    @ApiOperation(value = "开卡预申请")
    @PostMapping(value = "/openCardPre")
    @SentinelResource("aries-gw#goldOpenCardPre")
//    @PasswordVerify(value = EnumPwdVerify.PASSWORD)
    public IbgResultInfo<OpenCardPreVO> openCardPre(@RequestBody OpenCardPreParam param) {
        return new IbgResultInfo<OpenCardPreVO>().succeed(goldAccountService.openCardPre(param));
    }

    @ApiOperation(value = "开卡申请")
    @PostMapping(value = "/openCard")
    @SentinelResource("aries-gw#goldOpenCard")
    public IbgResultInfo<OpenCardVO> openCard(@RequestBody OpenCardParam param) {
        return new IbgResultInfo<OpenCardVO>().succeed(goldAccountService.openCard(param));
    }


    @ApiOperation(value = "开户进度查询")
    @PostMapping(value = "/openAccountProcessQuery")
    @SentinelResource("aries-gw#goldOpenAccountProcessQuery")
    public IbgResultInfo<OpenAccountProcessVO> openAccountProcessQuery(@RequestBody OpenAccountProcessParam param) {
        return new IbgResultInfo<OpenAccountProcessVO>().succeed(goldAccountService.openAccountProcessQuery(param));
    }

    @ApiOperation(value = "证件上传")
    @PostMapping(value = "/faceUpload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @SentinelResource("aries-gw#oldFaceUpload")
    public ResultInfo<String> faceUpload(BaseRequest param, @RequestPart("file") @NotBlank MultipartFile file) {
        return new ResultInfo<String>().succeed(goldAccountService.faceUpload(file));
    }

/*    @ApiOperation(value = "绑定卡校验")
    @PostMapping(value = "/bindCardCheck")
    @SentinelResource("aries-gw#goldBindCardCheck")
    public IbgResultInfo<BindCardCheckVO> bindCardCheck(@RequestBody BindCardCheckParam param) {
        return new IbgResultInfo<BindCardCheckVO>().succeed(goldAccountService.bindCardCheck(param));
    }*/


    @ApiOperation(value = "变更绑定卡预申请", notes = "当预申请失败时，返回特殊errorCode\n：" +
            "8001：身份证已过期->需更新身份证信息\n" +
            "8002：因近六个月无交易而锁定->需进行账户解锁\n" +
            "8003：无已签约入金通道->需进行入金通道签约\n" +
            "8004：未进行积存金签约->需进行积存金签约\n" +
            "8005：绑定卡变更失败->需进行非0资产变更申请\n " +
            "前端需根据以上code走对应的流程")
    @PostMapping(value = "/changeBindCardPre")
    @SentinelResource("aries-gw#goldChangeBindCardPre")
    public IbgResultInfo<ChangeBindCardPreVO> changeBindCardPre(@RequestBody ChangeBindCardPreParam param) {
        return new IbgResultInfo<ChangeBindCardPreVO>().succeed(goldAccountService.changeBindCardPre(param));
    }

    @ApiOperation(value = "变更绑定卡申请")
    @PostMapping(value = "/changeBindCard")
    @SentinelResource("aries-gw#goldChangeBindCard")
    public IbgResultInfo<ChangeBindCardVO> changeBindCard(@RequestBody ChangeBindCardParam param) {
        return new IbgResultInfo<ChangeBindCardVO>().succeed(goldAccountService.changeBindCard(param));
    }

    @ApiOperation(value = "变更绑定卡确认")
    @PostMapping(value = "/changeBindCardConfirm")
    @SentinelResource("aries-gw#goldChangeBindCardConfirm")
    public IbgResultInfo<ChangeBindCardConfirmVO> changeBindCardConfirm(@RequestBody ChangeBindCardConfirmParam param) {
        return new IbgResultInfo<ChangeBindCardConfirmVO>().succeed(goldAccountService.changeBindCardConfirm(param));
    }

    @ApiOperation(value = "变更手机号预申请")
    @PostMapping(value = "/changeMobilePre")
    @SentinelResource("aries-gw#goldChangeMobilePre")
    public IbgResultInfo<ChangeMobilePreVO> changeMobilePre(@RequestBody ChangeMobilePreParam param) {
        return new IbgResultInfo<ChangeMobilePreVO>().succeed(goldAccountService.changeMobilePre(param));
    }

    @ApiOperation(value = "变更手机号申请")
    @PostMapping(value = "/changeMobile")
    @SentinelResource("aries-gw#goldChangeMobile")
    public IbgResultInfo<ChangeMobileVO> changeMobile(@RequestBody ChangeMobileParam param) {
        return new IbgResultInfo<ChangeMobileVO>().succeed(goldAccountService.changeMobile(param));
    }

    @ApiOperation(value = "账户解锁预申请")
    @PostMapping(value = "/unLockAccountPre")
    @SentinelResource("aries-gw#goldUnLockAccountPre")
    public IbgResultInfo<AccountUnLockPreVO> unLockAccountPre(@RequestBody AccountUnLockPreParam param) {
        return new IbgResultInfo<AccountUnLockPreVO>().succeed(goldAccountService.unLockAccountPre(param));
    }

    @ApiOperation(value = "账户解锁申请")
    @PostMapping(value = "/unLockAccount")
    @SentinelResource("aries-gw#goldUnLockAccount")
    public IbgResultInfo<AccountUnLockVO> unLockAccount(@RequestBody AccountUnLockParam param) {
        return new IbgResultInfo<AccountUnLockVO>().succeed(goldAccountService.unLockAccount(param));
    }

    @ApiOperation(value = "账户注销预申请")
    @PostMapping(value = "/destroyAccountPre")
    @SentinelResource("aries-gw#goldDestroyAccountPre")
    //@PasswordVerify(value = EnumPwdVerify.PASSWORD)
    public IbgResultInfo<DestroyAccountPreVO> destroyAccountPre(@RequestBody DestroyAccountPreParam param) {
        return new IbgResultInfo<DestroyAccountPreVO>().succeed(goldAccountService.destroyAccountPre(param));
    }

    @ApiOperation(value = "账户注销申请")
    @PostMapping(value = "/destroyAccount")
    @SentinelResource("aries-gw#goldDestroyAccount")
    public IbgResultInfo<DestroyAccountVO> destroyAccount(@RequestBody DestroyAccountParam param) {
        return new IbgResultInfo<DestroyAccountVO>().succeed(goldAccountService.destroyAccount(param));
    }

    @ApiOperation(value = "获取二类卡余额")
    @PostMapping(value = "/balanceQuery")
    @SentinelResource("aries-gw#goldBalanceQuery")
    public IbgResultInfo<BalanceQueryVO> balanceQuery(@RequestBody BalanceQueryParam param) {
        return new IbgResultInfo<BalanceQueryVO>().succeed(goldAccountService.balanceQuery(param));
    }

    @ApiOperation(value = "区县代码列表 (前端自己根据名称模糊匹配)")
    @PostMapping(value = "/getAreaList")
    @SentinelResource("aries-gw#goldAreaList")
    public IbgResultInfo<List<GoldAreaInfoVO>> getAreaList(@RequestBody GoldAreaInfoParam param) {
        return new IbgResultInfo<List<GoldAreaInfoVO>>().succeed(goldAccountService.getAreaList(param));
    }

    @ApiOperation(value = "职业代码列表")
    @PostMapping(value = "/getJobList")
    @SentinelResource("aries-gw#goldJobList")
    public IbgResultInfo<List<JobInfoVO>> getJobList(@RequestBody JobInfoParam param) {
        return new IbgResultInfo<List<JobInfoVO>>().succeed(goldAccountService.getJobList(param));
    }

    @ApiOperation(value = "限额&&费率&支持绑定的的银行列表")
    @PostMapping(value = "/getBankConfig")
    @SentinelResource("aries-gw#goldBankConfig")
    public IbgResultInfo<BankConfigVO> getBankConfig(@RequestBody BankConfigParam param) {
        return new IbgResultInfo<BankConfigVO>().succeed(goldAccountService.getBankConfig(param));
    }

    @ApiOperation(value = "风险问题查询")
    @PostMapping(value = "/getRiskQuestion")
    @SentinelResource("aries-gw#goldRiskQuestion")
    public IbgResultInfo<RiskQuestionQueryVO> getRiskQuestion(@RequestBody RiskQuestionQueryParam param) {
        return new IbgResultInfo<RiskQuestionQueryVO>().succeed(goldAccountService.getRiskQuestion(param));
    }

    @ApiOperation(value = "风险测评问题提交")
    @PostMapping(value = "/commitRiskResult")
    @SentinelResource("aries-gw#commitRiskResult")
    public IbgResultInfo<RiskResultCommitVO> commitRiskResult(@RequestBody RiskResultCommitParam param) {
        return new IbgResultInfo<RiskResultCommitVO>().succeed(goldAccountService.commitRiskResult(param));
    }

    @ApiOperation(value = "入金签约预申请")
    @PostMapping(value = "/cashInSignPre")
    @SentinelResource("aries-gw#cashInSignPre")
    //@PasswordVerify(value = EnumPwdVerify.PASSWORD)
    public IbgResultInfo<CashInSignPreVO> cashInSignPre(@RequestBody CashInSignPreParam param) {
        return new IbgResultInfo<CashInSignPreVO>().succeed(goldAccountService.cashInSignPre(param));
    }

    @ApiOperation(value = "入金签约申请")
    @PostMapping(value = "/cashInSign")
    @SentinelResource("aries-gw#cashInSign")
    public IbgResultInfo<CashInSignVO> cashInSign(@RequestBody CashInSignParam param) {
        return new IbgResultInfo<CashInSignVO>().succeed(goldAccountService.cashInSign(param));
    }

    @ApiOperation(value = "积存金签约预申请", notes = "返回特殊errorCode：" +
            "8007-未进行风险测评或测评已过期 \n" +
            "前端需根据以上code走对应的流程")
    @PostMapping(value = "/goldSignPre")
    @SentinelResource("aries-gw#goldSignPre")
    //@PasswordVerify(value = EnumPwdVerify.PASSWORD)
    public IbgResultInfo<GoldSignPreVO> goldSignPre(@RequestBody GoldSignPreParam param) {
        return new IbgResultInfo<GoldSignPreVO>().succeed(goldAccountService.goldSignPre(param));
    }

    @ApiOperation(value = "积存金签约申请")
    @PostMapping(value = "/goldSign")
    @SentinelResource("aries-gw#goldSign")
    public IbgResultInfo<GoldSignVO> goldSign(@RequestBody GoldSignParam param) {
        return new IbgResultInfo<GoldSignVO>().succeed(goldAccountService.goldSign(param));
    }

    @ApiOperation(value = "适当性问卷提交")
    @PostMapping(value = "/accessResultCommit")
    @SentinelResource("aires-gw#accessResultCommit")
    public IbgResultInfo<AccessResultCommitVO> accessResultCommit(@RequestBody AccessResultCommitParam param) {
        return new IbgResultInfo<AccessResultCommitVO>().succeed(goldAccountService.accessResultCommit(param));
    }

    @ApiOperation(value = "获取适当性问卷")
    @PostMapping(value = "/getAssessQuestionnaire")
    @SentinelResource("aires-gw#getAssessQuestionnaire")
    public IbgResultInfo<AccessQuestionVO> getAssessQuestionnaire(@RequestBody AccessQuestionParam param) {
        return new IbgResultInfo<AccessQuestionVO>().succeed(goldAccountService.accessQuestion(param));
    }

    @ApiOperation(value = "获取风测授权协议书")
    @PostMapping(value = "/getRiskAuthBook")
    @SentinelResource("aires-gw#getRiskAuthBook")
    public IbgResultInfo<RiskAuthBookVO> getRiskAuthBook(@RequestBody RiskAuthBookParam param) {
        return new IbgResultInfo<RiskAuthBookVO>().succeed(goldAccountService.getRiskAuthBook(param));
    }

    @ApiOperation(value = "风测授权")
    @PostMapping(value = "/riskAuth")
    @SentinelResource("aires-gw#riskAuth")
    public IbgResultInfo<RiskAuthVO> riskAuth(@RequestBody RiskAuthParam param) {
        return new IbgResultInfo<RiskAuthVO>().succeed(goldAccountService.riskAuth(param));
    }

    @ApiOperation(value = "风测前置校验")
    @PostMapping(value = "/riskPreCheck")
    @SentinelResource("aires-gw#riskPreCheck")
    public IbgResultInfo<RiskPreCheckVO> riskPreCheck(@RequestBody RiskPreCheckParam param) {
        return new IbgResultInfo<RiskPreCheckVO>().succeed(goldAccountService.riskPreCheck(param));
    }

    @ApiOperation(value = "获取短信验证码", notes = "对于民生，入参中的mobileTel可以不传，因为开卡&更换手机号时从预申请入参中获取手机号；其他场景都是使用二类卡手机号")
    @PostMapping(value = "/sendSms")
    @SentinelResource("aries-gw#goldSendSms")
    public IbgResultInfo<SendSmsVO> sendSms(@RequestBody SendSmsParam param) {
        return new IbgResultInfo<SendSmsVO>().succeed(goldAccountService.sendSms(param));
    }

}
