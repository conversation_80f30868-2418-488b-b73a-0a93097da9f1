package com.ttfund.ibg.ariesgateway.config;

import com.alipay.sofa.tracer.plugins.springmvc.SpringMvcSofaTracerFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * sofa-tracer过滤器注册
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/27 15:37
 */
@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean<SpringMvcSofaTracerFilter> filterRegister() {

        FilterRegistrationBean<SpringMvcSofaTracerFilter> registration =
                new FilterRegistrationBean<>(new SpringMvcSofaTracerFilter());
        registration.addUrlPatterns("/*");
        registration.setOrder(1);
        return registration;
    }
}
