package com.ttfund.ibg.ariesgateway.service.gold;

import com.ttfund.ibg.ariesgateway.manager.GoldManager;
import com.ttfund.ibg.ariesgateway.util.GoldBuilderHelper;
import com.ttfund.ibg.gateway.core.context.TraceHelper;
import com.ttfund.ibg.gateway.goldcommon.model.enums.EnumCashOutState;
import com.ttfund.ibg.gateway.goldcommon.model.request.*;
import com.ttfund.ibg.gateway.goldcommon.model.request.dto.MarketInfoDTO;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.*;
import com.ttfund.ibg.gateway.goldcommon.service.GoldTradeService;
import com.ttfund.ibg.pisces.entity.PaginationData;
import com.ttfund.ibg.pisces.entity.resp.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 积存金：充值 申购 提现
 *
 * <AUTHOR>
 * @date 2025/3/12
 */
@Slf4j
@Service
public class GoldTradeServiceImpl implements GoldTradeService {

    @Resource
    private GoldManager goldManager;

    /**
     * 银行通知（系统维护信息等）
     *
     * @param param
     * @return
     */
    @Override
    public List<BankNoticeVO> getBankNotice(BankNoticeParam param) {

        List<BankNotice> dtoBankNoticeList = goldManager.getBankNotice(param.getGoldBankCode());
        return GoldBuilderHelper.buildBankNotice(dtoBankNoticeList);
    }

    /**
     * 黄金市场交易状态
     *
     * @param param
     * @return
     */
    @Override
    public MarketInfoVO getMarketInfo(MarketInfoParam param) {
        MarketInfoDTO dtoMarketInfo = goldManager.getMarketInfo(param.getGoldBankCode());
        return GoldBuilderHelper.buildMarketInfo(dtoMarketInfo);
    }

    /**
     * 买卖点
     *
     * @param param
     * @return
     */
    @Override
    public List<BuySellPointVO> getBuySellPoint(BuySellPointParam param) {
        GetBuySellPointResp dtoBuySellPoint = goldManager.getBuySellPoint(param.getGoldBankCode(), param.getInvestorId()
                , param.getBeginDate(), param.getEndDate());
        return GoldBuilderHelper.buildBuySellPoint(dtoBuySellPoint);
    }

    /**
     * 入金预申请
     *
     * @param param
     * @return
     */
    @Override
    public CashInPreVO cashInPre(CashInPreParam param) {
        CashInPreResp cashInPreResp = goldManager.cashInPre(param.getInvestorId(), param.getGoldBankCode(), param.getAppAmount());
        return GoldBuilderHelper.buildCashInPreVO(cashInPreResp);
    }

    /**
     * 入金
     *
     * @param param
     * @return
     */
    @Override
    public CashInVO cashIn(CashInParam param) {
        CashInfo cashInfo = goldManager.cashIn(param.getInvestorId(), param.getGoldBankCode(), param.getPreApplyId(),
                param.getSmsCode(), param.getSmsId());
        return GoldBuilderHelper.buildCashInVO(cashInfo);
    }

    /**
     * 出金预申请
     *
     * @param param
     * @return
     */
    @Override
    public CashOutPreVO cashOutPre(CashOutPreParam param) {
        CashOutPreResp cashOutPreResp = goldManager.cashOutPre(param.getInvestorId(), param.getGoldBankCode(), param.getAppAmount());
        return GoldBuilderHelper.buildCashOutPreVO(cashOutPreResp);
    }

    /**
     * 出金
     *
     * @param param
     * @return
     */
    @Override
    public CashOutVO cashOut(CashOutParam param) {
        List<CashInfo> cashInfoList = goldManager.cashOut(param.getInvestorId(), param.getGoldBankCode(), param.getPreApplyId(),
                param.getSmsCode(), param.getSmsId());

        List<CashInfo> successAppList = cashInfoList.stream()
                .filter(item -> item != null && com.ttfund.ibg.pisces.enums.EnumAppState.SUCCESS.equals(item.getAppState()))
                .collect(Collectors.toList());

        List<CashInfo> failAppList = cashInfoList.stream()
                .filter(item -> item != null && com.ttfund.ibg.pisces.enums.EnumAppState.FAIL.equals(item.getAppState()))
                .collect(Collectors.toList());
        CashInfo failApp = null;

        List<CashInfo> processAppList = cashInfoList.stream()
                .filter(item -> item != null && com.ttfund.ibg.pisces.enums.EnumAppState.PROCESS.equals(item.getAppState()))
                .collect(Collectors.toList());

        // 计算出金结果状态
        EnumCashOutState cashOutState;
        int successNum = successAppList.size();
        int failNum = failAppList.size();
        int processNum = processAppList.size();

        if (successNum > 0) {
            if (failNum == 0 && processNum == 0) {
                cashOutState = EnumCashOutState.SUCCESS;
            } else {
                cashOutState = EnumCashOutState.PART_SUCCESS;
            }
        } else if (failNum > 0) {
            failApp = failAppList.stream()
                    .findFirst()
                    .orElse(null);
            if (processNum == 0) {
                cashOutState = EnumCashOutState.FAIL;
            } else {
                cashOutState = EnumCashOutState.PART_FAIL;
            }
        } else if (processNum > 0) {
            cashOutState = EnumCashOutState.PROCESS;
        } else {
            return null;
        }

        CashOutVO result = new CashOutVO();
        result.setBusinType("cashOut");
        result.setAppState(cashOutState.getState());
        result.setFailReason(failApp != null ? failApp.getFailReason() : null);

        BigDecimal successAmount = successAppList.stream()
                .map(item -> item.getAppAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal failAmount = failAppList.stream()
                .map(item -> item.getAppAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal processAmount = processAppList.stream()
                .map(item -> item.getAppAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        result.setSuccessAppAmount(successAmount);
        result.setFailAppAmount(failAmount);
        result.setProcessAppAmount(processAmount);

        return result;
    }

    /**
     * 资金记录查询
     *
     * @param param
     * @return
     */
    @Override
    public PaginationData<CashListQueryVO> cashListQuery(CashListQueryParam param) {
        PaginationData<CashSimple> cashSimplePaginationData = goldManager.cashListQuery(param.getInvestorId(), param.getGoldBankCode(), param.getPageSize(),
                param.getPage(), param.getBeginDate(), param.getEndDate());
        return GoldBuilderHelper.buildPageCashSimple(cashSimplePaginationData);
    }

    /**
     * 交易总览查询
     *
     * @param param
     * @return
     */
    @Override
    public TradeSummaryVO tradeSummaryQuery(TradeSummaryQueryParam param) {
        TradeSummaryQueryResp dtoTradeSummary = goldManager.tradeSummaryQuery(param.getGoldBankCode(), param.getInvestorId()
                , param.getBeginDate(), param.getEndDate());
        return GoldBuilderHelper.buildSradeSummaryQuery(dtoTradeSummary);
    }

    /**
     * 交易记录查询
     *
     * @param param
     * @return
     */
    @Override
    public PaginationData<TradeInfoSimpleVO> tradeListQuery(TradeListQueryParam param) {
        PaginationData<TradeInfoSimple> dtoTradeInfoSimple = goldManager.tradeListQuery(param.getInvestorId()
                , param.getGoldBankCode(), param.getPageSize(), param.getPage(), param.getBeginDate(), param.getEndDate()
                , param.getAppStateList(), param.getBusinTypeList());
        return GoldBuilderHelper.buildTradeListQuery(dtoTradeInfoSimple);
    }

    /**
     * 交易详情查询
     *
     * @param param
     * @return
     */
    @Override
    public TradeInfoDetailVO tradeDetailQuery(TradeDetailQueryParam param) {
        TradeInfoDetail dtoTradeInfoDetail = goldManager.tradeDetailQuery(param.getAppSerialNo(), param.getGoldBankCode(), param.getInvestorId());
        return GoldBuilderHelper.buildTradeInfoDetail(dtoTradeInfoDetail);
    }

    /**
     * 积存金申购
     *
     * @param param
     * @return
     */
    @Override
    public PurchaseVO purchase(PurchaseParam param) {
        String clientIp = TraceHelper.getTraceInfo().getClientIp();
        GoldPurchaseResp purchaseResp = goldManager.purchase(param.getAppAmount(), param.getGoldBankCode(), clientIp,
                param.getInvestorId(), param.getPayType(), param.getAppPrice(), param.getPriceId(), param.getProductCode());
        return GoldBuilderHelper.buildPurchaseVO(purchaseResp);
    }

    /**
     * 积存金赎回
     *
     * @param param
     * @return
     */
    @Override
    public RedeemVO redeem(RedeemParam param) {
        String clientIp = TraceHelper.getTraceInfo().getClientIp();
        GoldRedeemResp redeemResp = goldManager.redeem(param.getAppVol(), param.getGoldBankCode(), param.getBusinType(),
                clientIp, param.getInvestorId(), param.getAppPrice(), param.getPriceId(), param.getProductCode());
        return GoldBuilderHelper.buildRedeemVO(redeemResp);
    }
}
