package com.ttfund.ibg.ariesgateway.config;

import cn.hutool.core.net.NetUtil;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.ttfund.ibg.ariesgateway.config.apollo.AppConfigReader;
import com.ttfund.ibg.gateway.common.model.alarm.AlarmProperties;
import com.ttfund.ibg.gateway.common.model.enums.EnumIbgBiz;
import com.ttfund.ibg.gateway.common.util.GwCommonHelper;
import com.ttfund.ibg.gateway.core.alarm.AlarmCore;
import com.ttfund.ibg.gateway.core.aspect.SentinelAspect;
import com.ttfund.ibg.gateway.core.aspect.TraceMarkAspect;
import com.ttfund.ibg.gateway.core.config.GwConfigReader;
import com.ttfund.ibg.gateway.core.config.GwFactory;
import com.ttfund.ibg.gateway.core.config.IbgAddressConfigReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/27 15:50
 */
@Configuration
@EnableApolloConfig()
public class GwFactoryConfig {

    @Value("${spring.application.name}")
    private String appName;

    @Value("${sys.zone}")
    private String zone;

    @Resource
    @Lazy
    private AlarmCore alarmCore;

    @Resource
    @Lazy
    private IbgAddressConfigReader ibgAddressConfigReader;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private AppConfigReader appConfigReader;

    @Bean
    public GwConfigReader gwConfigReader() {
        return new GwConfigReader();
    }

    @Bean
    public IbgAddressConfigReader ibgAddressConfigReader() {
        return new IbgAddressConfigReader();
    }

    @Bean
    public TraceMarkAspect traceMarkAspect() {
        return new TraceMarkAspect();
    }

    @Bean
    public SentinelAspect sentinelAspect() {
        return new SentinelAspect();
    }

    @Bean
    public LogAspect logAspect() {
        return new LogAspect();
    }

    @Bean
    public GwFactory gwFactory() {
        //启动时设置sentinel端口号
        System.setProperty("csp.sentinel.api.port", appConfigReader.getSentinelPort());

        GwFactory gwFactory = new GwFactory();
        gwFactory.setAppName(appName);
        gwFactory.setAlarmCore(alarmCore);
        gwFactory.setSentinelAppName(GwCommonHelper.generateSentinelAppName(appName, zone));
        gwFactory.setBizCode(EnumIbgBiz.ETF_GW.getBizCode());
        gwFactory.setSubBizCode(EnumIbgBiz.ETF_GW.getSubBizCode());
        gwFactory.setRestTemplate(restTemplate);
        gwFactory.setSecurityReference(appConfigReader.getSecurityReference());
        gwFactory.setZone(zone);
        gwFactory.setBridgeAuthKey("ARIES-GW");
        gwFactory.setSlowReqCollectEnabled(false);
        gwFactory.setPromEnabled(false);

        return gwFactory;
    }

    @Bean
    public AlarmCore alarmCore() {
        // 使用自定义的禁用版本 AlarmCore
        AlarmProperties properties = AlarmProperties.builder()
                .maxQueueSize(0)  // 设置为0禁用队列
                .appName("disabled")
                .ip("127.0.0.1")
                .noticeInstance(0L)
                .build();

        // 使用禁用版本的 AlarmCore，不会启动监控线程
        return new DisabledAlarmCore(properties, restTemplate, ibgAddressConfigReader);
    }

}
