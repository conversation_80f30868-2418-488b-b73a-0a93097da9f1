package com.ttfund.ibg.ariesgateway.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/20
 */
@Data
@Component
@ConfigurationProperties(prefix = "info")
@PropertySource(value = "classpath:version-info.yml", factory = YamlPropertySourceFactory.class, encoding = "utf-8")
public class VersionProperties {

    /**
     * 版本号
     */
    private String version;
    /**
     * 详情
     */
    private List<RequirementDetail> details;

    /**
     * 需求详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RequirementDetail {
        /**
         * 需求编号
         */
        private String id;
        /**
         * 需求内容
         */
        private String content;
        /**
         * 开发人
         */
        private String developers;
    }

    public static void main(String[] args) {
        String s = "asd";
    }
}
