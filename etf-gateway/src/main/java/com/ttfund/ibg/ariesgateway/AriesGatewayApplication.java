package com.ttfund.ibg.ariesgateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableRetry
@EnableCaching
@EnableScheduling
public class AriesGatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(AriesGatewayApplication.class, args);
    }

}
