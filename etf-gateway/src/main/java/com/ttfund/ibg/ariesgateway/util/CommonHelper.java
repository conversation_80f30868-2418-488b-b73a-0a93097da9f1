package com.ttfund.ibg.ariesgateway.util;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.ttfund.ibg.aries.entity.ResultInfo;
import com.ttfund.ibg.gateway.common.exception.GatewayException;
import com.ttfund.ibg.gateway.common.exception.IbgException;
import com.ttfund.ibg.gateway.common.model.enums.EnumGatewayException;
import com.ttfund.ibg.gateway.common.model.enums.EnumIbgBiz;
import com.ttfund.ibg.gateway.common.model.response.MidResultInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/27 17:24
 */
@Slf4j
public class CommonHelper {

    private CommonHelper() {
    }

    public static <T> T resolveResultInfo(ResultInfo<T> resultInfo, boolean isResultMustNotNull) {
        if (resultInfo == null) {
            throw new GatewayException(EnumGatewayException.ETF_INVOKE_FAIL);
        }
        boolean isNull = isResultMustNotNull && resultInfo.getResult() == null;
        if (!resultInfo.isSuccess() || isNull) {
            log.warn("etf接口调用失败:{}", resultInfo);
            throw new IbgException(EnumIbgBiz.ETF_ACCEPT.getBizCode(), resultInfo.getErrorCode(), resultInfo.getMessage());
        }
        return resultInfo.getResult();
    }

    public static <T> T resolveMidResult(MidResultInfo<T> resultInfo, boolean isResultMustNotNull) {
        if (resultInfo == null) {
            throw new GatewayException(EnumGatewayException.ETF_INVOKE_FAIL);
        }
        boolean isNull = isResultMustNotNull && resultInfo.getResult() == null;
        if (!resultInfo.getSucceed() || isNull) {
            log.warn("etf接口调用失败:{}", resultInfo);
            throw new IbgException(EnumIbgBiz.ETF_ACCEPT.getBizCode(), resultInfo.getErrorCode(), resultInfo.getMessage());
        }
        return resultInfo.getResult();
    }

    /**
     * 校验用户核心服务返回resultInfo，并返回result
     *
     * @param midResultInfo       用户核心服务返回的resultInfo
     * @param isResultMustNonNull result是否必须不为空
     * @return T result
     */
    public static <T> T resolveUserCoreResultInfo(MidResultInfo<T> midResultInfo, boolean isResultMustNonNull) {
        if (midResultInfo == null) {
            throw new GatewayException(EnumGatewayException.USER_CORE_INVOKE_FAIL);
        }
        if (!BooleanUtil.isTrue(midResultInfo.getSucceed())) {
            log.warn("invoke fail: {}", midResultInfo);
            if (!StrUtil.isBlank(midResultInfo.getMessage())) {
                throw new GatewayException(EnumGatewayException.USER_CORE_INVOKE_FAIL, midResultInfo.getMessage());
            } else if (midResultInfo.getErrorMessages() != null
                    && midResultInfo.getErrorMessages().length >= 2) {
                throw new GatewayException(EnumGatewayException.USER_CORE_INVOKE_FAIL, midResultInfo.getErrorMessages()[1]);
            }

            throw new GatewayException(EnumGatewayException.USER_CORE_INVOKE_FAIL, midResultInfo.getMessage());
        }
        if (isResultMustNonNull && midResultInfo.getResult() == null) {
            log.warn("result is null: {}", midResultInfo);
            throw new GatewayException(EnumGatewayException.USER_CORE_INVOKE_FAIL, midResultInfo.getMessage());
        }
        return midResultInfo.getResult();
    }

    /**
     * 银行账号显示过滤 - 获取银行卡4位尾号
     */
    public static String getMaskedShortBankNo(String str) {
        if (!StrUtil.isEmpty(str)) {
            int len = str.length();
            return len > 4 ? str.substring(len - 4, len) : str;
        }
        return null;
    }

    /**
     * 获取银行名称|银行卡尾号
     * <p>
     * 示例：
     * <ul>
     * <li>活期宝（工商银行 | 0234）</li>
     * <li>活期宝 (数字人民币-工商银行| 1234）</li>
     * <li>数字人民币 (工商银行 | 1234）</li>
     * <li>活期宝（支付宝-工商银行 | 2233）</li>
     * <li>支付宝（工商银行 | 2233）</li>
     * </ul>
     *
     * @param bankCode 银行代码
     * @param bankName 银行名称
     * @param isHqbBuy 是否活期宝支付
     * @return 银行名称|银行卡尾号
     */
    public static String getUserAccount(String bankCode, String bankName, String bankCardNo, Boolean isHqbBuy) {
        String bankType = getBankCardType(bankCode);
        String shortBankCardNo = getMaskedShortBankNo(bankCardNo);

        String userAccount = StrUtil.format("{} | {}", bankName, shortBankCardNo);

        switch (bankType) {
            case "0":
                // 普通银行
                if (BooleanUtil.isTrue(isHqbBuy)) {
                    userAccount = StrUtil.format("活期宝（{}）", userAccount);
                }
                break;
            case "1":
                // 数字人民币
                Pattern pattern = Pattern.compile("^数字人民币（(?<bankNM>.*)）$");
                Matcher matcher = pattern.matcher(bankName);
                if (matcher.find()) {
                    bankName = matcher.group("bankNM");
                }
                if (BooleanUtil.isTrue(isHqbBuy)) {
                    userAccount = StrUtil.format("活期宝（数字人民币-{} | {}）", bankName, shortBankCardNo);
                } else {
                    userAccount = StrUtil.format("数字人民币（{} | {}）", bankName, shortBankCardNo);
                }
                break;
            case "2":
                // 支付宝银行
                if (BooleanUtil.isTrue(isHqbBuy)) {
                    userAccount = StrUtil.format("活期宝（支付宝-{}）", userAccount);
                } else {
                    userAccount = StrUtil.format("支付宝（{}）", userAccount);
                }
                break;
            default:
        }
        return userAccount;
    }

    /**
     * 获取银行卡类型
     * 0 普通银行卡
     * 1 数字货币(数字人民币钱包)
     * 2 支付宝银行
     *
     * @param bankCode 银行代码
     * @return 银行卡类型
     */
    public static String getBankCardType(String bankCode) {
        if (StrUtil.isNotEmpty(bankCode)) {
            if (bankCode.startsWith("S")) {
                return "1";
            } else if (bankCode.startsWith("ZFB")) {
                return "2";
            }
        }
        return "0";
    }


    private final static DateTimeFormatter MM_dd_HH_mm_PATTERN = DateTimeFormatter.ofPattern("MM-dd HH:mm");

    /**
     * MM-dd HH:mm 格式
     */
    public static String parseToMMddHHmmPattern(LocalDateTime time) {
        if (time == null) {
            return null;
        }
        return MM_dd_HH_mm_PATTERN.format(time);
    }

}
