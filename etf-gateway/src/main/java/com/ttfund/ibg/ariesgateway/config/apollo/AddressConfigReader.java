package com.ttfund.ibg.ariesgateway.config.apollo;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * apollo地址配置类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/27 11:20
 */
@Slf4j
@Data
@Component
@EnableApolloConfig()
public class AddressConfigReader {

//    @Value("${zookeeper:null}")
//    private String zookeeper;

//    @Value("${unpaid-api:null}")
//    private String unpaidApi;

//    /**
//     * 基金通账户服务直连调用地址
//     */
//    @Value("${aries-account-url:null}")
//    private String ariesAccountUrl;

//    /**
//     * 基金通资产服务直连调用地址
//     */
//    @Value("${aries-assets-url:null}")
//    private String ariesAssetsUrl;

//    /**
//     * 基金通交易服务直连调用地址
//     */
//    @Value("${aries-business-url:null}")
//    private String ariesBusinessUrl;

//    /**
//     * 基金通交易查询服务直连调用地址
//     */
//    @Value("${aries-query-url:null}")
//    private String ariesQueryUrl;

//    /**
//     * 基金通产品服务直连调用地址
//     */
//    @Value("${aries-product-url:null}")
//    private String ariesProductUrl;



    /**
     * 用户核心业务api
     */
    @Value("#{${core-user-api:null}}")
    private Map<String, String> coreUserApi;

    /**
     * 互联网交易中台
     */
    @Value("${business-interface-api:null}")
    private String businessInterfaceApi;

    /**
     * 积存金中台地址
     */
    @Value("${gold-api:null}")
    private String goldApi;

    /**
     * 临时文件存储系统地址(王文昊）
     */
    @Value("${temporary-file-storage-system-api:null}")
    private String tsffApi;
}
