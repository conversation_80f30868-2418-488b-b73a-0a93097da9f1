package com.ttfund.ibg.ariesgateway.util;


import com.github.stuxuhai.jpinyin.ChineseHelper;
import com.github.stuxuhai.jpinyin.PinyinFormat;
import com.github.stuxuhai.jpinyin.PinyinHelper;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/19 18:35
 */


public class PinYinUtil {

    private static final Pattern compile = Pattern.compile("[\u4e00-\u9fa5]");

    /**
     * 转换为有声调的拼音字符串
     *
     * @param pinYinStr 汉字
     * @return 有声调的拼音字符串
     */
    public static String changeToMarkPinYin(String pinYinStr) {

        String tempStr = null;

        try {
            tempStr = PinyinHelper.convertToPinyinString(pinYinStr, " ", PinyinFormat.WITH_TONE_MARK);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return tempStr;

    }


    /**
     * 转换为数字声调字符串
     *
     * @param pinYinStr 需转换的汉字
     * @return 转换完成的拼音字符串
     */
    public static String changeToNumberPinYin(String pinYinStr) {

        String tempStr = null;

        try {
            tempStr = PinyinHelper.convertToPinyinString(pinYinStr, " ", PinyinFormat.WITH_TONE_NUMBER);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return tempStr;

    }

    /**
     * 转换为不带音调的拼音字符串
     *
     * @param pinYinStr 需转换的汉字
     * @return 拼音字符串
     */
    public static String changeToTonePinYin(String pinYinStr) {

        String tempStr = null;

        try {
            tempStr = PinyinHelper.convertToPinyinString(pinYinStr, "", PinyinFormat.WITHOUT_TONE);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return tempStr;

    }

    /**
     * 转换为每个汉字对应拼音首字母字符串
     *
     * @param pinYinStr 需转换的汉字
     * @return 拼音字符串
     */
    public static String changeToGetShortPinYin(String pinYinStr) {

        String tempStr = null;

        try {
            tempStr = PinyinHelper.getShortPinyin(pinYinStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return tempStr;

    }

    /**
     * 检查汉字是否为多音字
     *
     * @param pinYinStr 需检查的汉字
     * @return true 多音字，false 不是多音字
     */
    public static boolean checkPinYin(char pinYinStr) {

        boolean check = false;
        try {
            check = PinyinHelper.hasMultiPinyin(pinYinStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return check;
    }

    /**
     * 简体转换为繁体
     *
     * @param pinYinStr
     * @return
     */
    public static String changeToTraditional(String pinYinStr) {

        String tempStr = null;
        try {
            tempStr = ChineseHelper.convertToTraditionalChinese(pinYinStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return tempStr;

    }

    /**
     * 繁体转换为简体
     *
     * @param pinYinSt
     * @return
     */
    public static String changeToSimplified(String pinYinSt) {

        String tempStr = null;

        try {
            tempStr = ChineseHelper.convertToSimplifiedChinese(pinYinSt);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return tempStr;

    }

    public static boolean isChinese(char chinese) {
        return ChineseHelper.isChinese(chinese);
    }

    public static boolean isChinese(String con) {
        for (int i = 0; i < con.length(); i = i + 1) {
            if (!compile.matcher(String.valueOf(con.charAt(i))).find()) {
                return false;
            }
        }
        return true;
    }

    public static boolean isContainChinese(String str) {
        return compile.matcher(str).find();
    }

    public static void main(String[] args) {
        String str = "郑州市";
        PinYinUtil jp = new PinYinUtil();
        System.out.println(changeToMarkPinYin(str));
        System.out.println(changeToTonePinYin("重庆市"));
        System.out.println(changeToSimplified(str));
        System.out.println(checkPinYin('重'));
        System.out.println(changeToGetShortPinYin("很行"));
        System.out.println(changeToGetShortPinYin("HX"));
        System.out.println(changeToGetShortPinYin(changeToTraditional("龙潭虎穴")));
        System.out.println(isChinese('中'));
        System.out.println(isChinese('s'));
    }
}