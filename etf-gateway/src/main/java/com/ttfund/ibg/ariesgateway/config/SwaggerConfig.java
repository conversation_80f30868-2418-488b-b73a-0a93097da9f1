package com.ttfund.ibg.ariesgateway.config;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.Objects;

/**
 * Swagger配置类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/27 11:07
 */
@Configuration
public class SwaggerConfig {

    @Value("${spring.profiles.active}")
    private String profile;

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .apis(input -> {
                    String pkg = input.getHandlerMethod().getBeanType().getPackage().getName();
                    return !pkg.equals("com.ttfund.ibg.ariesgateway.controller.gold");
                })
                .paths(Objects.equals("dev", profile) ? PathSelectors.any() : PathSelectors.none())
                .build()
                .groupName("ETF");
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("ETF手机接口文档")
                .description("aries-gateway")
                .contact(new Contact("李萌", null, "<EMAIL>"))
                .version("1.0")
                .build();
    }

    @Bean
    public Docket createGoldRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfoGold())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.ttfund.ibg.ariesgateway.controller.gold"))
                .paths(Objects.equals("dev", profile) ? PathSelectors.any() : PathSelectors.none())
                .build()
                .groupName("积存金");
    }

    private ApiInfo apiInfoGold() {
        return new ApiInfoBuilder()
                .title("积存金手机接口文档")
                .description("积存金手机接口文档")
                .contact(new Contact("吕修义", "", "<EMAIL>"))
                .version("1.0")
                .build();
    }

}
