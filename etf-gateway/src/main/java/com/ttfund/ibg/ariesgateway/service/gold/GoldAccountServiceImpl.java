package com.ttfund.ibg.ariesgateway.service.gold;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.ttfund.ibg.ariesgateway.manager.AccountManager;
import com.ttfund.ibg.ariesgateway.manager.FileManager;
import com.ttfund.ibg.ariesgateway.manager.GoldManager;
import com.ttfund.ibg.ariesgateway.mapper.GoldBuilder;
import com.ttfund.ibg.ariesgateway.util.GoldBuilderHelper;
import com.ttfund.ibg.gateway.common.exception.GatewayException;
import com.ttfund.ibg.gateway.common.model.enums.EnumGatewayException;
import com.ttfund.ibg.gateway.goldcommon.model.request.*;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.GoldAccountInfo;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.*;
import com.ttfund.ibg.gateway.goldcommon.service.GoldAccountService;
import com.ttfund.ibg.pisces.entity.req.SmsInfo;
import com.ttfund.ibg.pisces.entity.req.*;
import com.ttfund.ibg.pisces.entity.resp.*;
import com.ttfund.ibg.pisces.enums.EnumFollowType;
import com.ttfund.ibg.pisces.utils.DateUtils;
import com.ttund.ibg.bridge.common.model.account.CustomerBankAcctDTO;
import com.ttund.ibg.bridge.common.model.account.CustomerDTO;
import com.ttund.ibg.bridge.common.model.account.CustomerDetailDTO;
import com.ttund.ibg.bridge.common.model.account.CustomerSecurityDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 积存金：账户信息开户、账户查询等
 *
 * <AUTHOR>
 * @date 2025/3/12
 */
@Slf4j
@Service
public class GoldAccountServiceImpl implements GoldAccountService {

    @Resource
    private GoldManager goldManager;

    @Resource
    AccountManager accountManager;

    @Resource
    FileManager fileManager;

    /**
     * 前置校验（证件/中国税收居民等）
     *
     * @param param 入参
     * @return
     */
    @Override
    public AccountValidVO accountValid(AccountValidParam param) {
        AccountValidVO res = new AccountValidVO();

        Integer errorType = Integer.valueOf("-1");
        CustomerDTO customerDTO = accountManager.getCustomerDTO(param.getInvestorId());
        if (!"0".equals(customerDTO.getCertificateType())) {
            errorType = Integer.valueOf(3);
        } else {
            if (customerDTO.getIdEndDate() != null) {
                if (LocalDateTime.now().isAfter(customerDTO.getIdEndDate())
                        || LocalDate.of(2099, 12, 31).equals(customerDTO.getIdEndDate().toLocalDate())) {
                    errorType = Integer.valueOf(5);
                }
            } else {
                errorType = Integer.valueOf(5);
            }
        }

        if (Integer.valueOf(0).compareTo(errorType) > 0) {
            CustomerDetailDTO customerDetailDTO = accountManager.getCustomerDetailDTO(param.getInvestorId());
            if (!"1".equals(customerDetailDTO.getTaxPlayerType())) {
                errorType = Integer.valueOf(4);
            } else if (!customerDetailDTO.isCerFileIsUpdate()
                    || Integer.valueOf(0).compareTo(customerDetailDTO.getCertAuditState()) == 0) {
                errorType = Integer.valueOf(6);
            }
        }

        res.setErrorType(errorType);
        return res;
    }

    /**
     * 账户信息查询
     *
     * @param param 入参
     * @return 开通状态
     */
    @Override
    public GoldAccountOverviewListVO getAccountOverviewList(GoldAccountOverviewParam param) {
        GoldAccountQueryResp dtoGoldAccountQueryResp = goldManager.goldAccountQuery(param.getGoldBankCode(), param.getInvestorId());
        GoldAccountOverviewListVO goldAccountOverviewListVO = GoldBuilderHelper.buildGoldAccountInfosVO(dtoGoldAccountQueryResp);

        if (StrUtil.isNotBlank(param.getGoldBankCode()) && Boolean.TRUE.equals(param.getIsQueryBalance())) {
            BalanceQueryResp dtoBalanceQueryResp = goldManager.balanceQuery(param.getGoldBankCode(), param.getInvestorId());
            if (goldAccountOverviewListVO != null && !CollectionUtils.isEmpty(goldAccountOverviewListVO.getGoldAccountList()) && dtoBalanceQueryResp != null) {
                goldAccountOverviewListVO.getGoldAccountList().get(0).setBalance(dtoBalanceQueryResp.getBalance());
            }
        }

        return goldAccountOverviewListVO;
    }

    /**
     * 进度查询
     *
     * @param param
     * @return
     */
    @Override
    public ProcessQueryVO processQuery(ProcessQueryParam param) {
        ProcessQueryResp processQueryResp = goldManager.processQuery(param.getGoldBankCode(), param.getInvestorId(), param.getFollowType());
        return GoldBuilderHelper.buildProcessQueryVO(processQueryResp);
    }

    /**
     * 身份证更新预申请
     *
     * @param param
     * @return
     */
    @Override
    public UpdateIdCardPreVO updateIdCardPre(UpdateIdCardPreParam param) {
        UpdateIdCardPreResp dtoUpdateIdCardPreResp = goldManager.updateIdCardPre(param.getGoldBankCode(), param.getInvestorId());
        return GoldBuilderHelper.buildUpdateIdCardPre(dtoUpdateIdCardPreResp);
    }

    /**
     * 身份证更新申请
     *
     * @param param
     * @return
     */
    @Override
    public UpdateIdCardVO updateIdCard(UpdateIdCardParam param) {
        UpdateIdCardReq updateIdCardReq = new UpdateIdCardReq();
        updateIdCardReq.setBankCode(param.getGoldBankCode());
        updateIdCardReq.setCustomerNo(param.getInvestorId());
        updateIdCardReq.setPreApplyId(param.getPreApplyId());
        com.ttfund.ibg.pisces.entity.req.SmsInfo dtoSmsInfo = new SmsInfo();
        if (param.getSmsInfo() != null) {
            dtoSmsInfo.setSmsCode(param.getSmsInfo().getSmsCode());
            dtoSmsInfo.setSmsId(param.getSmsInfo().getSmsId());
        }
        updateIdCardReq.setSmsInfo(dtoSmsInfo);
        UpdateIdCardResp dtoUpdateIdCardResp = goldManager.updateIdCard(updateIdCardReq);
        return GoldBuilderHelper.buildUpdateIdCard(dtoUpdateIdCardResp);
    }

    /**
     * 证件上传
     *
     * @param file
     * @return
     */
    @Override
    public String faceUpload(MultipartFile file) {
        org.springframework.core.io.Resource fileResource;
        try {
            fileResource = file.getResource();
        } catch (RuntimeException e) {
            log.error(e.getMessage());
            throw new GatewayException(EnumGatewayException.SYSTEM_INTERNAL_ANOMALY, "证件图片上传异常");
        }
        return fileManager.fileSave(fileResource);
    }

    /**
     * 开户进度查询
     *
     * @param param
     * @return
     */
    @Override
    public OpenAccountProcessVO openAccountProcessQuery(OpenAccountProcessParam param) {
        OpenAccountProcessResp openAccountProcessResp = goldManager.openAccountProcessQuery(param.getGoldBankCode(), param.getInvestorId());
        return GoldBuilderHelper.buildOpenAccountProcessVO(openAccountProcessResp);
    }

    @Override
    public GoldAccountInfo getGoldAccountInfo(GoldAccountInfoParam param) {
        GoldAccountQueryResp dtoGoldAccountQueryResp = goldManager.goldAccountQuery(param.getGoldBankCode(), param.getInvestorId());
        return GoldBuilderHelper.buildGoldAccountInfoVO(dtoGoldAccountQueryResp);
    }

    /**
     * 获取用户手机号（银行卡预留手机号/天天基金手机号）
     *
     * @param param
     * @return 银行卡预留手机号/天天基金手机号
     */
    @Override
    public MobileTelVO getMobileTel(MobileTelParam param) {
        String maskMobileTel = "";
        String encryptMobileTel = "";
        if (StrUtil.isNotBlank(param.getBankAccountNo())) {
            CustomerBankAcctDTO bankAccountDTO = accountManager.getBankAccountDTO(param.getInvestorId(), param.getBankAccountNo());
            maskMobileTel = bankAccountDTO.getMaskMobileTel();
            encryptMobileTel = bankAccountDTO.getEncryptMobileTel();
        } else {
            CustomerSecurityDTO customerSecuritySetting = accountManager.getCustomerSecuritySetting(param.getInvestorId());
            maskMobileTel = customerSecuritySetting.getMaskMobileTel();
            encryptMobileTel = customerSecuritySetting.getEncryptMobileTel();
        }

        MobileTelVO mobileTelVO = new MobileTelVO();
        mobileTelVO.setMaskMobileTel(maskMobileTel);
        mobileTelVO.setEncryptMobileTel(encryptMobileTel);
        return mobileTelVO;
    }

    @Override
    public RiskQuestionQueryVO getRiskQuestion(RiskQuestionQueryParam param) {

        RiskQuestionQryReq req = GoldBuilder.INSTANCE.buildRiskQuestionQry(param);
        RiskQuestionQryResp resp = goldManager.riskQuery(req);
        return GoldBuilderHelper.buildRiskQuestion(resp);
    }

    @Override
    public RiskResultCommitVO commitRiskResult(RiskResultCommitParam param) {
        RiskResultCommitReq req = GoldBuilder.INSTANCE.buildRiskResultCommit(param);
        RiskResultCommitResp resp = goldManager.riskCommit(req);
        return RiskResultCommitVO.builder()
                .riskLevel(resp.getRiskLevel())
                .riskState(resp.getRiskState() == null ? null : resp.getRiskState().getState())
                .riskExpiredTime(resp.getRiskExpiredTime() == null ? null : DateUtils.toLocalDateTime(resp.getRiskExpiredTime()))
                .statusList(resp.getStatusList())
                .diffissQuesIdList(resp.getDiffissQuesIdList())
                .build();
    }

    /**
     * 绑定卡校验
     *
     * @param param
     * @return
     */
    @Override
    public BindCardCheckVO bindCardCheck(BindCardCheckParam param) {
        CustomerBankAcctDTO bankAccountDTO = accountManager.getBankAccountDTO(param.getInvestorId(), param.getBankAccountNo());
        BindCardCheckResp bindCardCheckResp = goldManager.bindCardCheck(param.getGoldBankCode(), bankAccountDTO.getBankCardNo(), bankAccountDTO.getBankCode(), param.getInvestorId());
        return GoldBuilderHelper.buildBindCardCheck(bindCardCheckResp);
    }

    /**
     * 变更绑定卡预申请
     *
     * @param param 入参
     * @return
     */
    @Override
    public ChangeBindCardPreVO changeBindCardPre(ChangeBindCardPreParam param) {
        ChangeBindCardPreResp changeBindCardPreResp = goldManager.changeBindCardPre(param.getGoldBankCode(), EnumFollowType.getEnum(param.getChangeType())
                , param.getInvestorId(), param.getHoldImageId(), param.getNewBindBankCardNo(), param.getNewBindBankCode());
        return GoldBuilderHelper.buildChangeBindCardPre(changeBindCardPreResp);
    }

    /**
     * 变更绑定卡申请
     *
     * @param param 入参
     * @return
     */
    @Override
    public ChangeBindCardVO changeBindCard(ChangeBindCardParam param) {
        ChangeBindCardReq dtoChangeBindCardRep = new ChangeBindCardReq();
        dtoChangeBindCardRep.setBankCode(param.getGoldBankCode());
        dtoChangeBindCardRep.setCustomerNo(param.getInvestorId());
        dtoChangeBindCardRep.setPreApplyId(param.getPreApplyId());

        com.ttfund.ibg.pisces.entity.req.SmsInfo dtoSmsInfo = new SmsInfo();
        if (param.getSmsInfo() != null) {
            dtoSmsInfo.setSmsCode(param.getSmsInfo().getSmsCode());
            dtoSmsInfo.setSmsId(param.getSmsInfo().getSmsId());
        }
        dtoChangeBindCardRep.setSmsInfo(dtoSmsInfo);

        ChangeBindCardResp changeBindCardResp = goldManager.changeBindCard(dtoChangeBindCardRep);
        return GoldBuilderHelper.buildChangeBindCard(changeBindCardResp);
    }

    /**
     * 变更绑定卡确认
     *
     * @param param 入参
     * @return
     */
    @Override
    public ChangeBindCardConfirmVO changeBindCardConfirm(ChangeBindCardConfirmParam param) {
        ChangeBindCardCfmResp changeBindCardCfmResp = goldManager.changeBindCardConfirm(param.getGoldBankCode()
                , param.getCfmSmsCode(), param.getPreApplyId(), param.getInvestorId());
        return GoldBuilderHelper.buildChangeBindCardConfirm(changeBindCardCfmResp);
    }

    /**
     * 变更手机号预申请
     *
     * @param param 入参
     * @return
     */
    @Override
    public ChangeMobilePreVO changeMobilePre(ChangeMobilePreParam param) {
        ChangeMobilePreResp dtoChangeMobilePreResp = goldManager.changeMobilePre(param.getGoldBankCode(), EnumFollowType.getEnum(param.getChangeType())
                , param.getInvestorId(), param.getHoldImageId(), param.getMobileTelEncrypt(), param.getNewMobileTel());
        return GoldBuilderHelper.buildChangeMobilePre(dtoChangeMobilePreResp);
    }

    /**
     * 变更手机号申请
     *
     * @param param 入参
     * @return
     */
    @Override
    public ChangeMobileVO changeMobile(ChangeMobileParam param) {
        ChangeMobileReq dtoChangeMobileReq = new ChangeMobileReq();
        dtoChangeMobileReq.setBankCode(param.getGoldBankCode());
        dtoChangeMobileReq.setCustomerNo(param.getInvestorId());

        com.ttfund.ibg.pisces.entity.req.SmsInfo dtoNewSmsInfo = new SmsInfo();
        if (param.getNewMobileSmsInfo() != null) {
            dtoNewSmsInfo.setSmsCode(param.getNewMobileSmsInfo().getSmsCode());
            dtoNewSmsInfo.setSmsId(param.getNewMobileSmsInfo().getSmsId());
        }
        dtoChangeMobileReq.setNewMobileSmsInfo(dtoNewSmsInfo);

        com.ttfund.ibg.pisces.entity.req.SmsInfo dtoOldSmsInfo = new SmsInfo();
        if (param.getOldMobileSmsInfo() != null) {
            dtoOldSmsInfo.setSmsCode(param.getOldMobileSmsInfo().getSmsCode());
            dtoOldSmsInfo.setSmsId(param.getOldMobileSmsInfo().getSmsId());
        }
        dtoChangeMobileReq.setOldMobileSmsInfo(dtoOldSmsInfo);
        dtoChangeMobileReq.setPreApplyId(param.getPreApplyId());

        ChangeMobileResp dtoChangeMobileResp = goldManager.changeMobile(dtoChangeMobileReq);
        return GoldBuilderHelper.buildChangeMobile(dtoChangeMobileResp);
    }

    /**
     * 账户解锁预申请
     *
     * @param param 入参
     * @return
     */
    @Override
    public AccountUnLockPreVO unLockAccountPre(AccountUnLockPreParam param) {
        AccountUnLockPreResp dtoAccountUnLockPreResp = goldManager.unLockAccountPre(param.getGoldBankCode(), param.getInvestorId());
        return GoldBuilderHelper.buildAccountUnLockPre(dtoAccountUnLockPreResp);
    }

    /**
     * 账户解锁申请
     *
     * @param param 入参
     * @return
     */
    @Override
    public AccountUnLockVO unLockAccount(AccountUnLockParam param) {
        AccountUnLockReq accountUnLockReq = new AccountUnLockReq();
        accountUnLockReq.setBankCode(param.getGoldBankCode());
        accountUnLockReq.setCustomerNo(param.getInvestorId());
        accountUnLockReq.setPreApplyId(param.getPreApplyId());
        com.ttfund.ibg.pisces.entity.req.SmsInfo dtoSmsInfo = new SmsInfo();
        if (param.getSmsInfo() != null) {
            dtoSmsInfo.setSmsCode(param.getSmsInfo().getSmsCode());
            dtoSmsInfo.setSmsId(param.getSmsInfo().getSmsId());
        }
        accountUnLockReq.setSmsInfo(dtoSmsInfo);
        AccountUnLockResp dtoAccountUnLockResp = goldManager.unLockAccount(accountUnLockReq);
        return GoldBuilderHelper.buildAccountUnLock(dtoAccountUnLockResp);
    }

    /**
     * 账户注销预申请
     *
     * @param param 入参
     * @return
     */
    @Override
    public DestroyAccountPreVO destroyAccountPre(DestroyAccountPreParam param) {
        DestroyAccountPreResp dtoDestroyAccountPreResp = goldManager.destroyAccountPre(param.getGoldBankCode(), param.getInvestorId());
        return GoldBuilderHelper.buildDestroyAccountPre(dtoDestroyAccountPreResp);
    }

    /**
     * 账户注销申请
     *
     * @param param 入参
     * @return
     */
    @Override
    public DestroyAccountVO destroyAccount(DestroyAccountParam param) {
        DestroyAccountReq dtoDestroyAccountReq = new DestroyAccountReq();
        dtoDestroyAccountReq.setBankCode(param.getGoldBankCode());
        dtoDestroyAccountReq.setCustomerNo(param.getInvestorId());
        dtoDestroyAccountReq.setPreApplyId(param.getPreApplyId());
        com.ttfund.ibg.pisces.entity.req.SmsInfo dtoSmsInfo = new SmsInfo();
        if (param.getSmsInfo() != null) {
            dtoSmsInfo.setSmsCode(param.getSmsInfo().getSmsCode());
            dtoSmsInfo.setSmsId(param.getSmsInfo().getSmsId());
        }
        dtoDestroyAccountReq.setSmsInfo(dtoSmsInfo);
        DestroyAccountResp dtoDestroyAccountResp = goldManager.destroyAccount(dtoDestroyAccountReq);
        return GoldBuilderHelper.buildDestroyAccount(dtoDestroyAccountResp);
    }

    /**
     * 获取二类卡余额
     *
     * @param param
     * @return
     */
    @Override
    public BalanceQueryVO balanceQuery(BalanceQueryParam param) {
        BalanceQueryResp dtoBalanceQuery = goldManager.balanceQuery(param.getGoldBankCode(), param.getInvestorId());
        BalanceQueryVO balanceQueryVO = new BalanceQueryVO();
        if (dtoBalanceQuery != null) {
            balanceQueryVO.setBalance(dtoBalanceQuery.getBalance());
        }
        return balanceQueryVO;
    }

    /**
     * 区县代码列表 (前端自己根据名称模糊匹配)
     *
     * @param param
     * @return
     */
    @Override
    public List<GoldAreaInfoVO> getAreaList(GoldAreaInfoParam param) {
        List<AreaInfo> dtoAreaList = goldManager.getAreaList(param.getGoldBankCode());
        return GoldBuilderHelper.buildGoldAareInfos(dtoAreaList);
    }

    /**
     * 职业代码列表
     *
     * @param param
     * @return
     */
    @Override
    public List<JobInfoVO> getJobList(JobInfoParam param) {
        List<JobInfo> dtoJobList = goldManager.getJobList(param.getGoldBankCode());
        return GoldBuilderHelper.buildGoldJobInfos(dtoJobList);
    }

    /**
     * 支持的银行列表
     *
     * @param param
     * @return
     */
    @Override
    public BankConfigVO getBankConfig(BankConfigParam param) {
        BankConfig dtoBankConfig = goldManager.getBankConfig(param.getGoldBankCode());
        return GoldBuilderHelper.buildGoldBankConfig(dtoBankConfig);
    }

    /**
     * 获取短信验证码
     *
     * @param param
     * @return
     */
    @Override
    public SendSmsVO sendSms(SendSmsParam param) {
        SendSmsResp dtoSendSms = goldManager.sendSms(param.getGoldBankCode(), param.getInvestorId()
                , param.getMobileTel(), param.getPreApplyId());
        return GoldBuilderHelper.buildSendSms(dtoSendSms);
    }

    /**
     * 开卡预申请
     *
     * @param param
     * @return
     */
    @Override
    public OpenCardPreVO openCardPre(OpenCardPreParam param) {
        OpenAccountPreResp openAccountPreResp = goldManager.openCardPre(param.getInvestorId(), param.getGoldBankCode(), param.getBindBankCardNo(), param.getBindBankCode(),
                param.getMobileTel(), param.getMobileTelEncrypt(), param.getDistrictCode(), param.getDetailAddress(), param.getJobCode(), param.getJobRemark(),
                param.getCompany(), param.getFaceImageId());
        return GoldBuilderHelper.buildOpenCardPreVO(openAccountPreResp);
    }

    /**
     * 开卡申请
     *
     * @param param
     * @return
     */
    @Override
    public OpenCardVO openCard(OpenCardParam param) {
        OpenAccountResp openAccountResp = goldManager.openCard(param.getInvestorId(), param.getGoldBankCode(),
                param.getPreApplyId(), param.getSmsCode(), param.getSmsId());
        return GoldBuilderHelper.buildOpenCardVO(openAccountResp);
    }

    /**
     * 入金签约预申请
     *
     * @param param
     * @return
     */
    @Override
    public CashInSignPreVO cashInSignPre(CashInSignPreParam param) {
        CashInSignPreResp cashInSignPreResp = goldManager.cashInSignPre(param.getInvestorId(), param.getGoldBankCode(), param.getCashInChannel());
        return GoldBuilderHelper.buildCashInSignPreVO(cashInSignPreResp);
    }

    /**
     * 入金签约申请
     *
     * @param param
     * @return
     */
    @Override
    public CashInSignVO cashInSign(CashInSignParam param) {
        CashInSignResp cashInSignResp = goldManager.cashInSign(param.getInvestorId(), param.getGoldBankCode(),
                param.getPreApplyId(), param.getSmsCode(), param.getSmsId());
        return GoldBuilderHelper.buildCashInSignVO(cashInSignResp);
    }

    /**
     * 积存金签约预申请
     *
     * @param param
     * @return
     */
    @Override
    public GoldSignPreVO goldSignPre(GoldSignPreParam param) {
        GoldSignPreResp goldSignPreResp = goldManager.goldSignPre(param.getInvestorId(), param.getGoldBankCode());
        return GoldBuilderHelper.buildGoldSignPreVO(goldSignPreResp);
    }

    /**
     * 积存金签约申请
     *
     * @param param
     * @return
     */
    @Override
    public GoldSignVO goldSign(GoldSignParam param) {
        GoldSignResp goldSignResp = goldManager.goldSign(param.getInvestorId(), param.getGoldBankCode(),
                param.getPreApplyId(), param.getSmsCode(), param.getSmsId());
        return GoldBuilderHelper.buildGoldSignVO(goldSignResp);
    }

    @Override
    public AccessResultCommitVO accessResultCommit(AccessResultCommitParam param) {
        List<AnswerInfo> answerInfoList = param.getAnswerInfoList().stream().map(GoldBuilderHelper::buildAnswerInfo).collect(Collectors.toList());
        AccessResultCommitResp resp = goldManager.accessCommit(param.getInvestorId(), param.getBankCode(), param.getQuestionnaireKey(), answerInfoList);
        return new AccessResultCommitVO(resp.getState().getState());
    }

    @Override
    public AccessQuestionVO accessQuestion(AccessQuestionParam param) {
        AccessQuestionQryResp resultInfo = goldManager.getAssessQuestionnaire(param.getInvestorId(), param.getBankCode());
        return GoldBuilderHelper.buildAccessQuestion(resultInfo);
    }

    @Override
    public RiskAuthBookVO getRiskAuthBook(RiskAuthBookParam param) {
        RiskAuthBookResp resultInfo = goldManager.getRiskAuthBook(param.getInvestorId(), param.getBankCode());
        return RiskAuthBookVO.builder()
                .fileId(resultInfo.getFileId())
                .fileUrl(resultInfo.getFileUrl())
                .build();
    }

    @Override
    public RiskAuthVO riskAuth(RiskAuthParam param) {
        //返参暂无实体
        RiskAuthResp resultInfo = goldManager.riskAuth(param.getInvestorId(), param.getBankCode(), param.getFileId());
        return new RiskAuthVO();
    }

    @Override
    public RiskPreCheckVO riskPreCheck(RiskPreCheckParam param) {
        RiskPreCheckResp resultInfo = goldManager.riskPreCheck(param.getInvestorId(), param.getBankCode());
        return RiskPreCheckVO.builder()
                .authed(BooleanUtil.isTrue(resultInfo.getAuthed()))
                .riskExpiredTime(resultInfo.getRiskExpiredTime() == null ? null : DateUtils.toLocalDateTime(resultInfo.getRiskExpiredTime()))
                .riskLevel(resultInfo.getRiskLevel())
                .riskState(resultInfo.getRiskState() == null ? null : resultInfo.getRiskState().getState())
                .build();
    }
}
