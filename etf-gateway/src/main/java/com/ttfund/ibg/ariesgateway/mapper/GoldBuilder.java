package com.ttfund.ibg.ariesgateway.mapper;

import com.ttfund.ibg.gateway.goldcommon.model.request.RiskQuestionQueryParam;
import com.ttfund.ibg.gateway.goldcommon.model.request.RiskResultCommitParam;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.AccessQuestionVO;
import com.ttfund.ibg.pisces.entity.req.RiskQuestionQryReq;
import com.ttfund.ibg.pisces.entity.req.RiskResultCommitReq;
import com.ttfund.ibg.pisces.entity.resp.AccessQuestionQryResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> <EMAIL>
 * @date 2025/3/24
 */
@Mapper
public interface GoldBuilder {

    GoldBuilder INSTANCE = Mappers.getMapper(GoldBuilder.class);

    /**
     * 风测问题查询入参构建
     *
     * @param param
     * @return
     */
    @Mapping(source = "investorId", target = "customerNo")
    RiskQuestionQryReq buildRiskQuestionQry(RiskQuestionQueryParam param);


    /**
     * 风测答案提交入参构建
     *
     * @param param
     * @return
     */
    @Mapping(source = "investorId", target = "customerNo")
    RiskResultCommitReq buildRiskResultCommit(RiskResultCommitParam param);
}
