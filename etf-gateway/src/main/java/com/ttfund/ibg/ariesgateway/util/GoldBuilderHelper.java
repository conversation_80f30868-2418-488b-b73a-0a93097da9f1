package com.ttfund.ibg.ariesgateway.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.ttfund.ibg.ariesgateway.manager.AccountManager;
import com.ttfund.ibg.gateway.common.util.DateUtils;
import com.ttfund.ibg.gateway.common.util.GwDateUtils;
import com.ttfund.ibg.gateway.goldcommon.model.request.AccessResultCommitParam;
import com.ttfund.ibg.gateway.goldcommon.model.request.dto.MarketInfoDTO;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.GoldAccountInfo;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.*;
import com.ttfund.ibg.pisces.entity.PaginationData;
import com.ttfund.ibg.pisces.entity.req.AnswerInfo;
import com.ttfund.ibg.pisces.entity.resp.*;
import com.ttfund.ibg.pisces.enums.EnumFollowState;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 积存金接口帮助类
 *
 * <AUTHOR>
 * @date 2025/3/15
 */
public class GoldBuilderHelper {

    private static final AccountManager ACCOUNT_MANAGER = SpringFactory.getBean(AccountManager.class);

    public static final String MASK_PREFIX = "**** **** **** **** ";

    /**
     * 进度查询
     *
     * @param dtoProcessQueryResp
     * @return
     */
    public static ProcessQueryVO buildProcessQueryVO(ProcessQueryResp dtoProcessQueryResp) {
        ProcessQueryVO res = new ProcessQueryVO();
        if (dtoProcessQueryResp != null) {
            res.setFailReason(dtoProcessQueryResp.getFailReason());
            res.setNeedCfm(dtoProcessQueryResp.getNeedCfm());
            res.setPreApplyId(dtoProcessQueryResp.getPreApplyId());
            res.setProcessState(dtoProcessQueryResp.getProcessState().getState());
            res.setNewBindBankCardNo(dtoProcessQueryResp.getNewBindBankCardNo());
            res.setNewBindBankCode(dtoProcessQueryResp.getNewBindBankCode());
            res.setNewMobileMask(dtoProcessQueryResp.getNewMobileMask());
        }
        return res;
    }

    /**
     * 身份证更新申请
     *
     * @param dtoUpdateIdCardPreResp
     * @return
     */
    public static UpdateIdCardPreVO buildUpdateIdCardPre(UpdateIdCardPreResp dtoUpdateIdCardPreResp) {
        UpdateIdCardPreVO res = new UpdateIdCardPreVO();
        res.setNeedSms(dtoUpdateIdCardPreResp.isNeedSms());
        res.setPreApplyId(dtoUpdateIdCardPreResp.getPreApplyId());
        return res;
    }

    /**
     * 身份证更新申请
     *
     * @param dtoUpdateIdCardResp
     * @return
     */
    public static UpdateIdCardVO buildUpdateIdCard(UpdateIdCardResp dtoUpdateIdCardResp) {
        UpdateIdCardVO res = new UpdateIdCardVO();
        res.setState(dtoUpdateIdCardResp.getState().getState());
        res.setFailReason(dtoUpdateIdCardResp.getFailReason());
        return res;
    }

    /**
     * 开户进度（各业务节点状态)
     *
     * @param dtoAccountProcess
     * @return
     */
    public static OpenAccountProcessVO buildOpenAccountProcessVO(OpenAccountProcessResp dtoAccountProcess) {
        OpenAccountProcessVO res = new OpenAccountProcessVO();

        if (dtoAccountProcess.getIdCardUploadInfo() != null) {
            OpenAccountProcessVO.ProcessNode idCardUploadInfo = new OpenAccountProcessVO.ProcessNode();

            ProcessNode dtoIdCardUpload = dtoAccountProcess.getIdCardUploadInfo();
            idCardUploadInfo.setProcessState(dtoIdCardUpload.getProcessState().getState());
            idCardUploadInfo.setFailReason(dtoIdCardUpload.getFailReason());

            res.setIdCardUploadInfo(idCardUploadInfo);
        }

        if (dtoAccountProcess.getOpenBankCardInfo() != null) {
            OpenAccountProcessVO.ProcessNode openBankCardInfo = new OpenAccountProcessVO.ProcessNode();

            ProcessNode dtoOpenBankCard = dtoAccountProcess.getOpenBankCardInfo();
            openBankCardInfo.setProcessState(dtoOpenBankCard.getProcessState().getState());
            openBankCardInfo.setFailReason(dtoOpenBankCard.getFailReason());

            res.setOpenBankCardInfo(openBankCardInfo);
        }

        return res;
    }

    /**
     * 账户信息 列表
     *
     * @param dtoAccounts
     * @return
     */
    public static GoldAccountOverviewListVO buildGoldAccountInfosVO(GoldAccountQueryResp dtoAccounts) {
        if (dtoAccounts == null || CollectionUtils.isEmpty(dtoAccounts.getGoldAccountList())) {
            return null;
        }

        GoldAccountOverviewListVO res = new GoldAccountOverviewListVO();
        List<GoldAccountInfo> infoVOList = new ArrayList<>();
        for (com.ttfund.ibg.pisces.entity.resp.GoldAccountInfo item : dtoAccounts.getGoldAccountList()) {

            GoldAccountInfo infoVO = new GoldAccountInfo();
            if (item.getAccessState() != null) {
                infoVO.setAccessState(item.getAccessState().getState());
            }
            if (item.getAccountState() != null) {
                infoVO.setAccountState(item.getAccountState().getState());
            }
            infoVO.setGoldBankCode(item.getBankCode());
            infoVO.setGoldAccountId(item.getGoldAccountId());
            infoVO.setGoldBankCardNoMask(MASK_PREFIX + CommonHelper.getMaskedShortBankNo(item.getBankCardNo()));
            infoVO.setGoldBankCardNo(item.getBankCardNo());
            String showBindAccount = CommonHelper.getUserAccount(item.getBankCode(), ACCOUNT_MANAGER.getBankShortName(item.getBindBankCode()), item.getBindBankCardNo(), false);
            infoVO.setBindBankCardNoMask(showBindAccount);
            infoVO.setBindBankCode(item.getBindBankCode());
            infoVO.setMaskMobileTel(item.getMaskMobileTel());
            infoVO.setEncryptMobileTel(item.getEncryptMobileTel());
            infoVO.setBankServerTel(item.getBankServerTel());

            if (item.getCashInSignState() != null) {
                infoVO.setCashInSignState(item.getCashInSignState().getState());
            }
            if (item.getRiskState() != null) {
                infoVO.setRiskState(item.getRiskState().getState());
            }
            infoVO.setRiskLevel(item.getRiskLevel());
            if (item.getGoldSignState() != null) {
                infoVO.setGoldSignState(item.getGoldSignState().getState());
            }
            infoVOList.add(infoVO);

        }

        res.setGoldAccountList(infoVOList);
        return res;
    }

    /**
     * 二类户账户信息（明细）
     *
     * @param dtoAccounts
     * @return
     */
    public static GoldAccountInfo buildGoldAccountInfoVO(GoldAccountQueryResp dtoAccounts) {
        if (dtoAccounts == null || CollectionUtils.isEmpty(dtoAccounts.getGoldAccountList())) {
            return null;
        }
        com.ttfund.ibg.pisces.entity.resp.GoldAccountInfo dtoGoldAccountInfo = dtoAccounts.getGoldAccountList().get(0);

        GoldAccountInfo res = new GoldAccountInfo();
        res.setGoldBankCode(dtoGoldAccountInfo.getBankCode());
        res.setGoldAccountId(dtoGoldAccountInfo.getGoldAccountId());
        res.setGoldBankCardNo(dtoGoldAccountInfo.getBankCardNo());

        return res;
    }

    /**
     * 绑定卡校验
     *
     * @param dtoBindCardCheck
     * @return
     */
    public static BindCardCheckVO buildBindCardCheck(BindCardCheckResp dtoBindCardCheck) {
        BindCardCheckVO res = new BindCardCheckVO();
        res.setSupportOpenAccount(dtoBindCardCheck.getSupportOpenAccount());
        res.setNotSupportReason(dtoBindCardCheck.getNotSupportReason());
        return res;
    }

    /**
     * 变更绑定卡预申请
     *
     * @param dtoChangeBindCardPre
     * @return
     */
    public static ChangeBindCardPreVO buildChangeBindCardPre(ChangeBindCardPreResp dtoChangeBindCardPre) {
        ChangeBindCardPreVO res = new ChangeBindCardPreVO();
        res.setNeedSms(dtoChangeBindCardPre.isNeedSms());
        res.setPreApplyId(dtoChangeBindCardPre.getPreApplyId());
        return res;
    }

    /**
     * 变更绑定卡申请
     *
     * @param dtoChangeBindCard
     * @return
     */
    public static ChangeBindCardVO buildChangeBindCard(ChangeBindCardResp dtoChangeBindCard) {
        ChangeBindCardVO res = new ChangeBindCardVO();
        res.setChangeState(dtoChangeBindCard.getChangeState().getState());
        res.setFailReason(dtoChangeBindCard.getFailReason());
        return res;
    }

    /**
     * 变更绑定卡确认
     *
     * @param dtoChangeBindCardCfm
     * @return
     */
    public static ChangeBindCardConfirmVO buildChangeBindCardConfirm(ChangeBindCardCfmResp dtoChangeBindCardCfm) {
        ChangeBindCardConfirmVO res = new ChangeBindCardConfirmVO();
        res.setCfmState(dtoChangeBindCardCfm.getCfmState().getState());
        res.setFailReason(dtoChangeBindCardCfm.getFailReason());
        return res;
    }

    /**
     * 变更手机号预申请
     *
     * @param dtoChangeMobilePreResp
     * @return
     */
    public static ChangeMobilePreVO buildChangeMobilePre(ChangeMobilePreResp dtoChangeMobilePreResp) {
        ChangeMobilePreVO res = new ChangeMobilePreVO();
        res.setEncryptMobileTel(dtoChangeMobilePreResp.getEncryptMobileTel());
        res.setMaskMobileTel(dtoChangeMobilePreResp.getMaskMobileTel());
        res.setNewMobileNeedSms(dtoChangeMobilePreResp.isNewMobileNeedSms());
        res.setOldMobileNeedSms(dtoChangeMobilePreResp.isOldMobileNeedSms());
        res.setPreApplyId(dtoChangeMobilePreResp.getPreApplyId());
        return res;
    }

    /**
     * 变更手机号申请
     *
     * @param dtoChangeMobileResp
     * @return
     */
    public static ChangeMobileVO buildChangeMobile(ChangeMobileResp dtoChangeMobileResp) {
        ChangeMobileVO res = new ChangeMobileVO();
        res.setChangeState(dtoChangeMobileResp.getChangeState().getState());
        res.setFailReason(dtoChangeMobileResp.getFailReason());
        return res;
    }

    /**
     * 账户解锁预申请
     *
     * @param dtoAccountUnLockPreResp
     * @return
     */
    public static AccountUnLockPreVO buildAccountUnLockPre(AccountUnLockPreResp dtoAccountUnLockPreResp) {
        AccountUnLockPreVO res = new AccountUnLockPreVO();
        res.setNeedSms(dtoAccountUnLockPreResp.isNeedSms());
        res.setPreApplyId(dtoAccountUnLockPreResp.getPreApplyId());
        return res;
    }

    /**
     * 账户解锁申请
     *
     * @param dtoAccountUnLockResp
     * @return
     */
    public static AccountUnLockVO buildAccountUnLock(AccountUnLockResp dtoAccountUnLockResp) {
        AccountUnLockVO res = new AccountUnLockVO();
        res.setUnLockState(dtoAccountUnLockResp.getUnLockState().getState());
        res.setFailReason(dtoAccountUnLockResp.getFailReason());
        return res;
    }

    /**
     * 账户注销预申请
     *
     * @param dtoDestroyAccountPreResp
     * @return
     */
    public static DestroyAccountPreVO buildDestroyAccountPre(DestroyAccountPreResp dtoDestroyAccountPreResp) {
        DestroyAccountPreVO res = new DestroyAccountPreVO();
        res.setNeedSms(dtoDestroyAccountPreResp.isNeedSms());
        res.setPreApplyId(dtoDestroyAccountPreResp.getPreApplyId());
        return res;
    }

    /**
     * 账户注销申请
     *
     * @param dtoDestroyAccountResp
     * @return
     */
    public static DestroyAccountVO buildDestroyAccount(DestroyAccountResp dtoDestroyAccountResp) {
        DestroyAccountVO res = new DestroyAccountVO();
        res.setDestroyState(dtoDestroyAccountResp.getDestroyState().getState());
        res.setFailReason(dtoDestroyAccountResp.getFailReason());
        return res;
    }

    /**
     * 区县代码列表
     *
     * @param dtoAreaInfos
     * @return
     */
    public static List<GoldAreaInfoVO> buildGoldAareInfos(List<AreaInfo> dtoAreaInfos) {
        List<GoldAreaInfoVO> goldAreaInfoVOList = new ArrayList<>();
        if (dtoAreaInfos == null || CollectionUtils.isEmpty(dtoAreaInfos)) {
            return goldAreaInfoVOList;
        }
        for (AreaInfo dtoAreaInfo : dtoAreaInfos) {
            GoldAreaInfoVO areaInfoVO = new GoldAreaInfoVO();
            areaInfoVO.setAreaCode(dtoAreaInfo.getAreaCode());
            areaInfoVO.setAreaName(dtoAreaInfo.getAreaName());
            areaInfoVO.setParentId(dtoAreaInfo.getParentId());
            areaInfoVO.setChildAreaList(dtoAreaInfo.getSonAreaList());

            goldAreaInfoVOList.add(areaInfoVO);
        }

        return goldAreaInfoVOList;
    }

    public static List<JobInfoVO> buildGoldJobInfos(List<JobInfo> dtoJobInfos) {
        List<JobInfoVO> goldAreaInfoVOList = new ArrayList<>();
        if (dtoJobInfos == null || CollectionUtils.isEmpty(dtoJobInfos)) {
            return goldAreaInfoVOList;
        }
        for (JobInfo dtoJobInfo : dtoJobInfos) {
            JobInfoVO jobInfoVO = new JobInfoVO();
            jobInfoVO.setJobCode(dtoJobInfo.getJobCode());
            jobInfoVO.setJobName(dtoJobInfo.getJobName());
            jobInfoVO.setParentId(dtoJobInfo.getParentId());
            jobInfoVO.setChildJobList(dtoJobInfo.getSonJobList());

            goldAreaInfoVOList.add(jobInfoVO);
        }

        return goldAreaInfoVOList;
    }

    public static BankConfigVO buildGoldBankConfig(BankConfig dtoBankConfig) {
        BankConfigVO res = new BankConfigVO();
        res.setMinBuyAmount(dtoBankConfig.getMinBuyAmount());
        res.setMinBuyWeight(dtoBankConfig.getMinBuyWeight());
        res.setMaxBuyAmount(dtoBankConfig.getMaxBuyAmount());
        res.setMaxBuyWeight(dtoBankConfig.getMaxBuyWeight());
        res.setBuyRate(dtoBankConfig.getBuyRate());
        res.setSellRate(dtoBankConfig.getSellRate());
        res.setManageRate(dtoBankConfig.getManageRate());
        res.setSupportBankCode(dtoBankConfig.getSupportBankCode());
        res.setBuyAmountUnit(dtoBankConfig.getBuyAmountUnit());
        return res;
    }

    public static RiskQuestionQueryVO buildRiskQuestion(RiskQuestionQryResp resp) {
        RiskQuestionQueryVO result = new RiskQuestionQueryVO();
        result.setQuestionnaireInfo(buildRiskQuestionnaireInfo(resp.getQuestionnaireInfo()));
        return result;
    }

    public static RiskQuestionQueryVO.QuestionnaireInfo buildRiskQuestionnaireInfo(QuestionnaireInfo info) {
        RiskQuestionQueryVO.QuestionnaireInfo result = new RiskQuestionQueryVO.QuestionnaireInfo();
        result.setQuestionnaireKey(info.getQuestionnaireKey());
        result.setQuestionnaireTitle(info.getQuestionnaireTitle());
        result.setQuestionInfoList(info.getQuestionInfoList().stream().map(GoldBuilderHelper::buildRiskQuestionInfo).collect(Collectors.toList()));
        return result;
    }

    public static RiskQuestionQueryVO.QuestionInfo buildRiskQuestionInfo(QuestionInfo info) {
        RiskQuestionQueryVO.QuestionInfo result = new RiskQuestionQueryVO.QuestionInfo();
        result.setQuestion(info.getQuestion());
        result.setOptType(info.getOptType().getType());
        result.setQuestionKey(info.getQuestionKey());
        result.setOptList(info.getOptList().stream().map(GoldBuilderHelper::buildRiskQuesOptInfo).collect(Collectors.toList()));
        result.setOptJumpInfoList(info.getOptJumpInfoList().stream().map(GoldBuilderHelper::buildRiskQuesOptJumpInfo).collect(Collectors.toList()));
        return result;
    }

    public static RiskQuestionQueryVO.QuesOptInfo buildRiskQuesOptInfo(QuesOptInfo info) {
        RiskQuestionQueryVO.QuesOptInfo result = new RiskQuestionQueryVO.QuesOptInfo();
        result.setOptContent(info.getOptContent());
        result.setOptKey(info.getOptKey());
        result.setSelected(BooleanUtil.isTrue(info.getSelected()));
        result.setIsOptional(BooleanUtil.isTrue(info.getOptional()));
        return result;
    }

    public static RiskQuestionQueryVO.QuesOptJumpInfo buildRiskQuesOptJumpInfo(QuesOptJumpInfo info) {
        RiskQuestionQueryVO.QuesOptJumpInfo result = new RiskQuestionQueryVO.QuesOptJumpInfo();
        result.setLinkQuesOptKey(info.getLinkQuesOptKey());
        result.setOptKey(info.getOptKey());
        result.setLinkQuestionKey(info.getLinkQuestionKey());
        return result;
    }

    /**
     * 开卡预申请
     *
     * @param dto
     * @return
     */
    public static OpenCardPreVO buildOpenCardPreVO(OpenAccountPreResp dto) {
        OpenCardPreVO res = new OpenCardPreVO();
        res.setPreApplyId(dto.getPreApplyId());
        res.setNeedSms(dto.isNeedSms());
        return res;
    }

    /**
     * 开卡申请
     *
     * @param dto
     * @return
     */
    public static OpenCardVO buildOpenCardVO(OpenAccountResp dto) {
        OpenCardVO res = new OpenCardVO();
        res.setFailReason(dto.getFailReason());
        res.setOpenResult(dto.getOpenResult() == null ? EnumFollowState.PROCESS.getState() : dto.getOpenResult().getState());
        return res;
    }

    /**
     * 入金签约预申请
     *
     * @param dto
     * @return
     */
    public static CashInSignPreVO buildCashInSignPreVO(CashInSignPreResp dto) {
        CashInSignPreVO res = new CashInSignPreVO();
        res.setCashInSignState(dto.getCashInSignState().getState());
        res.setPreApplyId(dto.getPreApplyId());
        res.setNeedSms(dto.isNeedSms());
        return res;
    }

    /**
     * 入金签约申请
     *
     * @param dto
     * @return
     */
    public static CashInSignVO buildCashInSignVO(CashInSignResp dto) {
        CashInSignVO res = new CashInSignVO();
        res.setSignState(dto.getSignState() == null ? EnumFollowState.PROCESS.getState() : dto.getSignState().getState());
        res.setFailReason(dto.getFailReason());
        return res;
    }

    /**
     * 积存金签约预申请
     *
     * @param dto
     * @return
     */
    public static GoldSignPreVO buildGoldSignPreVO(GoldSignPreResp dto) {
        GoldSignPreVO res = new GoldSignPreVO();
        res.setGoldSignState(dto.getGoldSignState().getState());
        res.setPreApplyId(dto.getPreApplyId());
        res.setNeedSms(dto.isNeedSms());
        return res;
    }

    /**
     * 积存金签约申请
     *
     * @param dto
     * @return
     */
    public static GoldSignVO buildGoldSignVO(GoldSignResp dto) {
        GoldSignVO res = new GoldSignVO();
        res.setSignState(dto.getSignState() == null ? EnumFollowState.PROCESS.getState() : dto.getSignState().getState());
        res.setFailReason(dto.getFailReason());
        return res;
    }

    /**
     * 银行通知（系统维护信息等）
     *
     * @param dtoBankNoticeList
     * @return
     */
    public static List<BankNoticeVO> buildBankNotice(List<BankNotice> dtoBankNoticeList) {
        List<BankNoticeVO> res = new ArrayList<>();
        if (dtoBankNoticeList == null || CollectionUtils.isEmpty(dtoBankNoticeList)) {
            return res;
        }
        for (BankNotice item : dtoBankNoticeList) {
            BankNoticeVO vo = new BankNoticeVO();
            vo.setBeginTime(GwDateUtils.dateToLocalDateTime(item.getBeginTime()));
            vo.setContent(item.getContent());
            vo.setEndTime(GwDateUtils.dateToLocalDateTime(item.getEndTime()));
            vo.setLevel(item.getLevel());
            vo.setLinkUrl(item.getLinkUrl());
            vo.setTitle(item.getTitle());
            res.add(vo);
        }
        return res;
    }

    /**
     * 黄金市场交易状态
     *
     * @param dtoMarketInfo
     * @return
     */
    public static MarketInfoVO buildMarketInfo(MarketInfoDTO dtoMarketInfo) {
        MarketInfoVO res = new MarketInfoVO();
        res.setState(dtoMarketInfo.getState().name());
        res.setNextTradeTime(GwDateUtils.dateToLocalDateTime(dtoMarketInfo.getNextTradeTime()));
        switch (dtoMarketInfo.getState()) {
            case Trading:
                res.setStateTip("交易中");
                break;
            case Pause:
            case Closed:
                res.setStateTip(StrUtil.format("{}开盘", CommonHelper.parseToMMddHHmmPattern(res.getNextTradeTime())));
                break;
        }
        return res;
    }

    /**
     * 获取短信验证码
     *
     * @param dtoSendSms
     * @return
     */
    public static SendSmsVO buildSendSms(SendSmsResp dtoSendSms) {
        SendSmsVO res = new SendSmsVO();
        res.setMaskMobileTel(dtoSendSms.getMaskMobileTel());
        res.setSmsId(dtoSendSms.getSmsId());
        return res;
    }

    /**
     * 买卖点
     *
     * @param dtoBuySellPoint
     * @return
     */
    public static List<BuySellPointVO> buildBuySellPoint(GetBuySellPointResp dtoBuySellPoint) {
        List<BuySellPointVO> res = new ArrayList<>();
        BuySellPointVO detail;
        if (dtoBuySellPoint != null && !CollectionUtils.isEmpty(dtoBuySellPoint.getPointList())) {
            for (TradePointInfo dtoItem : dtoBuySellPoint.getPointList()) {
                detail = new BuySellPointVO();
                detail.setPointType(dtoItem.getPointType().name());
                detail.setTime(GwDateUtils.dateToLocalDateTime(dtoItem.getTime()));
                res.add(detail);
            }
        }
        return res;
    }

    public static CashInPreVO buildCashInPreVO(CashInPreResp dto) {
        CashInPreVO res = new CashInPreVO();
        res.setPreApplyId(dto.getPreApplyId());
        res.setNeedSms(dto.isNeedSms());
        return res;
    }

    public static CashInVO buildCashInVO(CashInfo dto) {
        CashInVO res = new CashInVO();
        res.setBusinType(dto.getBusinType() == null ? null : dto.getBusinType().getType());
        res.setAppTime(DateUtils.dateToLocalDateTime(dto.getAppTime()));
        res.setAppAmount(dto.getAppAmount());
        res.setAppState(dto.getAppState() == null ? null : dto.getAppState().getState());
        res.setFailReason(dto.getFailReason());
        res.setBankCode(dto.getBankCode());
        res.setBankCardNo(dto.getBankCardNo());
        res.setBindBankCardNo(dto.getBindBankCardNo());
        res.setBindBankCode(dto.getBindBankCode());
        return res;
    }

    public static CashOutPreVO buildCashOutPreVO(CashOutPreResp dto) {
        CashOutPreVO res = new CashOutPreVO();
        res.setPreApplyId(dto.getPreApplyId());
        res.setNeedSms(dto.isNeedSms());
        return res;
    }

    public static PaginationData<CashListQueryVO> buildPageCashSimple(PaginationData<CashSimple> dto) {
        if (dto == null) {
            return null;
        }
        PaginationData<CashListQueryVO> res = new PaginationData<>();
        res.setTotalPages(dto.getTotalPages());
        res.setCurrentPage(dto.getCurrentPage());
        res.setPageSize(dto.getPageSize());

        List<CashListQueryVO> resData = new ArrayList<>();
        List<CashSimple> dtoData = dto.getData();
        if (CollUtil.isNotEmpty(dtoData)) {
            for (CashSimple dtoItem : dtoData) {
                CashListQueryVO resItem = CashListQueryVO.builder()
                        .businType(dtoItem.getBusinType() == null ? null : dtoItem.getBusinType().getType())
                        .tradeTime(DateUtils.dateToLocalDateTime(dtoItem.getTradeTime()))
                        .appAmount(dtoItem.getAppAmount())
                        .appState(dtoItem.getAppState() == null ? null : dtoItem.getAppState().getState())
                        .bankCode(dtoItem.getBankCode())
                        .bankCardNo(MASK_PREFIX + CommonHelper.getMaskedShortBankNo(dtoItem.getBankCardNo()))
                        .dfBankCardNo(MASK_PREFIX + CommonHelper.getMaskedShortBankNo(dtoItem.getDfBankCardNo()))
                        .dfBankName(dtoItem.getDfBankName())
                        .build();
                resData.add(resItem);
            }
        }
        res.setData(resData);
        return res;
    }

    /**
     * 交易总览查询
     *
     * @param dtoTradeSummary
     * @return
     */
    public static TradeSummaryVO buildSradeSummaryQuery(TradeSummaryQueryResp dtoTradeSummary) {
        TradeSummaryVO res = new TradeSummaryVO();
        res.setBuySuccessTimes(dtoTradeSummary.getBuySuccessTimes());
        res.setBuyTotalCfmAmount(dtoTradeSummary.getBuyTotalCfmAmount());
        res.setBuyTotalCharge(dtoTradeSummary.getBuyTotalCharge());
        res.setHlpxTimes(dtoTradeSummary.getHlpxTimes());
        res.setHlpxTotalCfmVol(dtoTradeSummary.getHlpxTotalCfmVol());
        res.setSellSuccessTimes(dtoTradeSummary.getSellSuccessTimes());
        res.setSellTotalCfmAmount(dtoTradeSummary.getSellTotalCfmAmount());
        res.setSellTotalCharge(dtoTradeSummary.getSellTotalCharge());
        return res;
    }

    /**
     * 交易总览查询
     *
     * @param dtoTradeInfoSimple
     * @return
     */
    public static PaginationData<TradeInfoSimpleVO> buildTradeListQuery(PaginationData<TradeInfoSimple> dtoTradeInfoSimple) {
        PaginationData<TradeInfoSimpleVO> res = new PaginationData<>();
        res.setCurrentPage(dtoTradeInfoSimple.getCurrentPage());
        res.setPageSize(dtoTradeInfoSimple.getPageSize());
        res.setTotalPages(dtoTradeInfoSimple.getTotalPages());

        List<TradeInfoSimpleVO> resData = new ArrayList<>();
        TradeInfoSimpleVO vo;
        List<TradeInfoSimple> dtoData = dtoTradeInfoSimple.getData();
        if (CollUtil.isNotEmpty(dtoData)) {
            for (TradeInfoSimple dtoItem : dtoData) {
                vo = new TradeInfoDetailVO();
                vo.setAppAmount(dtoItem.getAppAmount());
                vo.setAppPrice(dtoItem.getAppPrice());
                vo.setAppSerialNo(dtoItem.getAppSerialNo());
                vo.setAppState(dtoItem.getAppState().getState());
                vo.setAppTime(GwDateUtils.dateToLocalDateTime(dtoItem.getAppTime()));
                vo.setAppVol(dtoItem.getAppVol());
                vo.setGoldBankCode(dtoItem.getBankCode());
                vo.setBusinType(dtoItem.getBusinType().getType());
                vo.setCfmAmount(dtoItem.getCfmAmount());
                vo.setCfmPrice(dtoItem.getCfmPrice());
                vo.setCfmVol(dtoItem.getCfmVol());
                vo.setCharge(dtoItem.getCharge());
                vo.setChargeRate(dtoItem.getChargeRate());
                resData.add(vo);
            }
        }
        res.setData(resData);
        return res;
    }

    public static AnswerInfo buildAnswerInfo(AccessResultCommitParam.AnswerInfo info) {
        AnswerInfo result = new AnswerInfo();
        result.setOptKeySet(info.getOptKeySet());
        result.setQuestionKey(info.getQuestionKey());
        return result;
    }

    public static AccessQuestionVO buildAccessQuestion(AccessQuestionQryResp resp) {
        AccessQuestionVO result = new AccessQuestionVO();
        result.setQuestionnaireInfo(buildAccessQuestionnaireInfo(resp.getQuestionnaireInfo()));
        return result;
    }

    public static AccessQuestionVO.QuestionnaireInfo buildAccessQuestionnaireInfo(QuestionnaireInfo info) {
        AccessQuestionVO.QuestionnaireInfo result = new AccessQuestionVO.QuestionnaireInfo();
        result.setQuestionnaireKey(info.getQuestionnaireKey());
        result.setQuestionnaireTitle(info.getQuestionnaireTitle());
        result.setQuestionInfoList(info.getQuestionInfoList().stream().map(GoldBuilderHelper::buildAccessQuestionInfo).collect(Collectors.toList()));
        return result;
    }

    public static AccessQuestionVO.QuestionInfo buildAccessQuestionInfo(QuestionInfo info) {
        AccessQuestionVO.QuestionInfo result = new AccessQuestionVO.QuestionInfo();
        result.setQuestion(info.getQuestion());
        result.setOptType(info.getOptType().getType());
        result.setQuestionKey(info.getQuestionKey());
        result.setOptList(info.getOptList().stream().map(GoldBuilderHelper::buildAccessQuesOptInfo).collect(Collectors.toList()));
        result.setOptJumpInfoList(info.getOptJumpInfoList().stream().map(GoldBuilderHelper::buildAccessQuesOptJumpInfo).collect(Collectors.toList()));
        return result;
    }

    public static AccessQuestionVO.QuesOptInfo buildAccessQuesOptInfo(QuesOptInfo info) {
        AccessQuestionVO.QuesOptInfo result = new AccessQuestionVO.QuesOptInfo();
        result.setOptContent(info.getOptContent());
        result.setOptKey(info.getOptKey());
        result.setSelected(BooleanUtil.isTrue(info.getSelected()));
        result.setIsOptional(BooleanUtil.isTrue(info.getOptional()));
        result.setScore(info.getScore());
        return result;
    }

    public static AccessQuestionVO.QuesOptJumpInfo buildAccessQuesOptJumpInfo(QuesOptJumpInfo info) {
        AccessQuestionVO.QuesOptJumpInfo result = new AccessQuestionVO.QuesOptJumpInfo();
        result.setLinkQuesOptKey(info.getLinkQuesOptKey());
        result.setOptKey(info.getOptKey());
        result.setLinkQuestionKey(info.getLinkQuestionKey());
        return result;
    }

    /**
     * 交易详情查询
     *
     * @param dtoTradeInfoDetail
     * @return
     */
    public static TradeInfoDetailVO buildTradeInfoDetail(TradeInfoDetail dtoTradeInfoDetail) {
        TradeInfoDetailVO res = new TradeInfoDetailVO();
        res.setAppAmount(dtoTradeInfoDetail.getAppAmount());
        res.setAppPrice(dtoTradeInfoDetail.getAppPrice());
        res.setAppSerialNo(dtoTradeInfoDetail.getAppSerialNo());
        res.setAppState(dtoTradeInfoDetail.getAppState().getState());
        res.setAppTime(GwDateUtils.dateToLocalDateTime(dtoTradeInfoDetail.getAppTime()));
        res.setAppVol(dtoTradeInfoDetail.getAppVol());
        res.setGoldBankCode(dtoTradeInfoDetail.getBankCode());
        res.setBankCardNo(CommonHelper.getMaskedShortBankNo(dtoTradeInfoDetail.getBankCardNo()));
        res.setBindBankCode(dtoTradeInfoDetail.getBindBankCode());
        res.setBindBankCardNo(CommonHelper.getMaskedShortBankNo(dtoTradeInfoDetail.getBindBankCardNo()));
        res.setBusinType(dtoTradeInfoDetail.getBusinType().getType());
        res.setCfmAmount(dtoTradeInfoDetail.getCfmAmount());
        res.setCfmPrice(dtoTradeInfoDetail.getCfmPrice());
        res.setCfmVol(dtoTradeInfoDetail.getCfmVol());
        res.setCharge(dtoTradeInfoDetail.getCharge());
        res.setChargeRate(dtoTradeInfoDetail.getChargeRate());
        res.setFailReason(dtoTradeInfoDetail.getFailReason());
        if (dtoTradeInfoDetail.getLinkRedeemState() != null) {
            res.setLinkRedeemState(dtoTradeInfoDetail.getLinkRedeemState().getState());
        }
        if (dtoTradeInfoDetail.getPayType() != null) {
            res.setPayType(dtoTradeInfoDetail.getPayType().getType());
        }
        res.setExtraInfos(dtoTradeInfoDetail.getExtraInfos());
        return res;
    }

    /**
     * 持仓摘要
     *
     * @param dtoHoldShareSummary
     * @return
     */
    public static HoldShareSummaryVO buildHoldShareSummary(HoldShareSummary dtoHoldShareSummary) {
        HoldShareSummaryVO res = new HoldShareSummaryVO();
        res.setAmount(dtoHoldShareSummary.getAsset());
        res.setCalcDate(GwDateUtils.dateToLocalDate(dtoHoldShareSummary.getCalcDate()));
        res.setExpectedBuyCount(dtoHoldShareSummary.getExpectedBuyCount());
        res.setExpectedSellCount(dtoHoldShareSummary.getExpectedSellCount());
        res.setFirstTradeDate(GwDateUtils.dateToLocalDate(dtoHoldShareSummary.getFirstTradeDate()));
        res.setHoldCost(dtoHoldShareSummary.getHoldCost());
        res.setHoldProfit(dtoHoldShareSummary.getHoldProfit());
        res.setHoldProfitRate(dtoHoldShareSummary.getHoldProfitRate());
        res.setOnUpdate(dtoHoldShareSummary.getOnUpdate());
        res.setPositionCost(dtoHoldShareSummary.getPositionCost());
        res.setPositionProfit(dtoHoldShareSummary.getPositionProfit());
        res.setPositionProfitRate(dtoHoldShareSummary.getPositionProfitRate());
        res.setTodayProfit(dtoHoldShareSummary.getTodayProfit());
        res.setTodayProfitRate(dtoHoldShareSummary.getTodayProfitRate());
        res.setTotalProfit(dtoHoldShareSummary.getTotalProfit());
        res.setTotalProfitRate(dtoHoldShareSummary.getTotalProfitRate());
        res.setTotalProfitThisRound(dtoHoldShareSummary.getTotalProfitThisRound());
        res.setWeight(dtoHoldShareSummary.getWeight());
        res.setBuildDate(GwDateUtils.dateToLocalDate(dtoHoldShareSummary.getBuildDate()));
        res.setBuildDays(dtoHoldShareSummary.getBuildDays());
        return res;
    }

    /**
     * 总收益/总收益率
     *
     * @param dtoTotalProfitInfo
     * @return
     */
    public static TotalProfitInfoVO buildTotalProfitInfo(TotalProfitInfo dtoTotalProfitInfo) {
        TotalProfitInfoVO res = new TotalProfitInfoVO();
        res.setBeginDate(GwDateUtils.dateToLocalDate(dtoTotalProfitInfo.getBeginDate()));
        res.setEndDate(GwDateUtils.dateToLocalDate(dtoTotalProfitInfo.getEndDate()));
        res.setTotalProfit(dtoTotalProfitInfo.getTotalProfit());
        res.setTotalProfitRate(dtoTotalProfitInfo.getTotalProfitRate());
        return res;
    }

    /**
     * 区间资产信息
     *
     * @param dtoPeriodAsset
     * @return
     */
    public static PeriodAssetVO buildPeriodAsset(PeriodAsset dtoPeriodAsset) {
        PeriodAssetVO res = new PeriodAssetVO();
        if (dtoPeriodAsset != null) {
            res.setAmount(dtoPeriodAsset.getAsset());
            res.setBeginDate(GwDateUtils.dateToLocalDate(dtoPeriodAsset.getBeginDate()));
            res.setBuyAmount(dtoPeriodAsset.getBuyAmount());
            res.setBuyCharge(dtoPeriodAsset.getBuyCharge());
            res.setBuyCount(dtoPeriodAsset.getBuyCount());
            res.setBuyWeight(dtoPeriodAsset.getBuyWeight());
            res.setEndDate(GwDateUtils.dateToLocalDate(dtoPeriodAsset.getEndDate()));
            res.setGiftWeight(dtoPeriodAsset.getGiftWeight());
            res.setInterestWeight(dtoPeriodAsset.getInterestWeight());
            res.setLastPeriodAsset(dtoPeriodAsset.getLastPeriodAsset());
            res.setLastPeriodWeight(dtoPeriodAsset.getLastPeriodWeight());
            res.setPeriodProfit(dtoPeriodAsset.getPeriodProfit());
            res.setPeriodProfitRate(dtoPeriodAsset.getPeriodProfitRate());
            res.setSellAmount(dtoPeriodAsset.getSellAmount());
            res.setSellCharge(dtoPeriodAsset.getSellCharge());
            res.setSellCount(dtoPeriodAsset.getSellCount());
            res.setSellWeight(dtoPeriodAsset.getSellWeight());
            res.setWeight(dtoPeriodAsset.getWeight());
            res.setNetAsset(dtoPeriodAsset.getBuyAmount().subtract(dtoPeriodAsset.getSellAmount()));
            res.setNetWeight(dtoPeriodAsset.getBuyWeight().subtract(dtoPeriodAsset.getSellWeight()));
        }
        return res;
    }

    /**
     * 收益趋势图
     *
     * @param dtoProfitTrendChart
     * @return
     */
    public static List<ProfitTrendChartVO> buildProfitTrendChart(ProfitTrendChart dtoProfitTrendChart) {
        List<ProfitTrendChartVO> res = new ArrayList<>();
        ProfitTrendChartVO detail;
        if (dtoProfitTrendChart != null && CollUtil.isNotEmpty(dtoProfitTrendChart.getEntityList())) {
            for (ProfitTrendChart.ChartEntity dtoItem : dtoProfitTrendChart.getEntityList()) {
                detail = new ProfitTrendChartVO();
                detail.setDate(GwDateUtils.dateToLocalDate(dtoItem.getDate()));
                detail.setProfit(dtoItem.getValue());
                res.add(detail);
            }
        }
        return res;
    }

    /**
     * 收益日历图
     *
     * @param dtoProfitCalendarChart
     * @return
     */
    public static List<ChartEntityVO> buildProfitCalendarChart(ProfitCalendarChart dtoProfitCalendarChart) {
        List<ChartEntityVO> res = new ArrayList<>();
        ChartEntityVO detail;
        if (dtoProfitCalendarChart != null && CollUtil.isNotEmpty(dtoProfitCalendarChart.getEntityList())) {
            for (ProfitCalendarChart.ChartEntity dtoItem : dtoProfitCalendarChart.getEntityList()) {
                detail = new ChartEntityVO();
                detail.setBeginDate(GwDateUtils.dateToLocalDate(dtoItem.getBeginDate()));
                detail.setEndDate(GwDateUtils.dateToLocalDate(dtoItem.getEndDate()));
                detail.setPeriod(dtoItem.getPeriod());
                detail.setProfit(dtoItem.getProfit());
                detail.setProfitRate(dtoItem.getProfitRate());
                res.add(detail);
            }
        }
        return res;
    }

    /**
     * 构建PurchaseVO
     *
     * @param resp
     * @return
     */
    public static PurchaseVO buildPurchaseVO(GoldPurchaseResp resp) {
        if (resp == null) {
            return null;
        }
        PurchaseVO result = new PurchaseVO();
        result.setAppSerialNo(resp.getAppSerialNo());
        result.setBankCode(resp.getBankCode());
        result.setAppState(resp.getAppState() == null ? null : resp.getAppState().getState());
        result.setFailReason(resp.getFailReason());
        result.setAppAmount(resp.getAppAmount());
        result.setAppTime(DateUtils.dateToLocalDateTime(resp.getAppTime()));
        result.setCfmPrice(resp.getCfmPrice());
        result.setCfmVol(resp.getCfmVol());
        result.setCfmAmount(resp.getCfmAmount());
        result.setCharge(resp.getCharge());
        result.setChargeRate(resp.getChargeRate());
        result.setPayType(resp.getPayType() == null ? null : resp.getPayType().getType());
        result.setBusinType(resp.getBusinType() == null ? null : resp.getBusinType().getType());
        return result;
    }

    /**
     * 构建RedeemVO
     *
     * @param resp
     * @return
     */
    public static RedeemVO buildRedeemVO(GoldRedeemResp resp) {
        if (resp == null) {
            return null;
        }
        RedeemVO result = new RedeemVO();
        result.setAppSerialNo(resp.getAppSerialNo());
        result.setBankCode(resp.getBankCode());
        result.setAppState(resp.getAppState() == null ? null : resp.getAppState().getState());
        result.setFailReason(resp.getFailReason());
        result.setAppTime(DateUtils.dateToLocalDateTime(resp.getAppTime()));
        result.setAppVol(resp.getAppVol());
        result.setCfmPrice(resp.getCfmPrice());
        result.setCfmVol(resp.getCfmVol());
        result.setCfmAmount(resp.getCfmAmount());
        result.setCharge(resp.getCharge());
        result.setChargeRate(resp.getChargeRate());
        result.setBusinType(resp.getBusinType() == null ? null : resp.getBusinType().getType());
        return result;
    }
}
