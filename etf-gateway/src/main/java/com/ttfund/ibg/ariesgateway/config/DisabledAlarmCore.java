package com.ttfund.ibg.ariesgateway.config;

import com.ttfund.ibg.gateway.common.model.alarm.AlarmProperties;
import com.ttfund.ibg.gateway.core.alarm.AlarmCore;
import com.ttfund.ibg.gateway.core.config.IbgAddressConfigReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.client.RestTemplate;

/**
 * 禁用的 AlarmCore 实现
 * 重写关键方法，避免启动监控相关的线程和功能
 *
 * <AUTHOR>
 * @date 2025/07/31
 */
@Slf4j
public class DisabledAlarmCore extends AlarmCore {

    public DisabledAlarmCore(AlarmProperties properties, RestTemplate restTemplate, IbgAddressConfigReader addressConfigReader) {
        super(properties, restTemplate, addressConfigReader);
    }

    @Override
    public void start() {
        log.info("AlarmCore 已禁用，跳过启动监控功能");
        // 不调用 super.start()，避免启动监控线程
    }
}
