package com.ttfund.ibg.ariesgateway.config.apollo;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/27 13:15
 */
@Slf4j
@Data
@Component
@EnableApolloConfig
public class AppConfigReader {

    @Value("${sys.alarm.maxQueueSize:1000}")
    private Integer maxQueueSize;

    @Value("${sys.alarm.appName:null}")
    private String appName;

    @Value("${sys.alarm.notice-instance:60000}")
    private Long noticeInstance;

    /**
     * 新安全中心所需的来源标识
     */
    @Value("${sys.security-reference:null}")
    private String securityReference;

    @Value("${rpc.uniqueId:null}")
    private String uniqueId;

    /**
     * 是否直连调用
     */
    @Value("${isDirect:false}")
    private Boolean isDirect;

    @Value("${csp.sentinel.api.port:8720}")
    private String sentinelPort;
}
