package com.ttfund.ibg.ariesgateway.manager;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.ttfund.base.model.entities.EnumType;
import com.ttfund.ibg.aries.entity.ConfigInfo;
import com.ttfund.ibg.aries.entity.ResultInfo;
import com.ttfund.ibg.aries.entity.param.OpenAccountParam;
import com.ttfund.ibg.aries.entity.response.AccountInfoResp;
import com.ttfund.ibg.aries.entity.vo.AccountInfoVO;
import com.ttfund.ibg.ariesgateway.config.apollo.AddressConfigReader;
import com.ttfund.ibg.ariesgateway.util.CommonHelper;
import com.ttfund.ibg.ariesgateway.util.ZoneHelper;
import com.ttfund.ibg.gateway.common.annotation.TraceMark;
import com.ttfund.ibg.gateway.common.exception.GatewayException;
import com.ttfund.ibg.gateway.common.model.enums.EnumGatewayException;
import com.ttfund.ibg.gateway.common.model.response.BusinessResultInfo;
import com.ttfund.ibg.gateway.common.model.response.MidResultInfo;
import com.ttfund.ibg.gateway.common.util.BankCardHelper;
import com.ttfund.ibg.gateway.core.context.TraceHelper;
import com.ttfund.ibg.gateway.core.context.TraceInfo;
import com.ttfund.ibg.gateway.core.util.GwBuildHelper;
import com.ttund.ibg.bridge.common.model.account.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/27 17:21
 */
@Slf4j
@Component
public class AccountManager {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private AddressConfigReader addressConfigReader;

    /**
     * 获取银行简称
     *
     * @param bankCode 银行代码
     * @return 银行简称
     */
    @Cacheable(value = "getBankShortName", cacheManager = "durableCacheManager")
    public String getBankShortName(String bankCode) {
        String key = "BankCode" + bankCode;
        EnumType enumType = getEnumType(key);
        if (enumType == null) {
            return "";
        }
        return enumType.getName();
    }

    /**
     * 获取交易中台枚举配置信息
     *
     * @param key key
     * @return EnumType
     */
    @Cacheable(value = "getEnumType", cacheManager = "durableCacheManager")
    public EnumType getEnumType(String key) {
        String url = addressConfigReader.getBusinessInterfaceApi() + "/config/fetchEnumTypeByKey?key={1}";
        BusinessResultInfo<EnumType> resultInfo = restTemplate.exchange(url, HttpMethod.GET, null,
                new ParameterizedTypeReference<BusinessResultInfo<EnumType>>() {
                }, key).getBody();
        if (resultInfo == null || !resultInfo.isSucceed()) {
            throw new GatewayException(EnumGatewayException.SYSTEM_INTERNAL_ANOMALY);
        }
        return resultInfo.getResult();
    }

    /**
     * 获取用户路由信息
     *
     * @param investorId 用户编号
     * @return RouteDTO 用户路由信息
     */
    @Retryable(Exception.class)
    @Cacheable(value = "getInvestorRoute", cacheManager = "durableCacheManager")
    public RouteDTO getInvestorRoute(String investorId) {
        String url = ZoneHelper.getDefaultUserCoreApi() + "/api/Router/GetRouteInfoByCustomerNo";
        Map<String, String> paramMap = new HashMap<>(1, 1);
        paramMap.put("CustomerNo", investorId);

        HttpEntity<Map<String, String>> request = GwBuildHelper.buildHttpEntity(paramMap, MediaType.APPLICATION_JSON, false);
        RouteDTO resultInfo = restTemplate.postForObject(url, request, RouteDTO.class);
        if (resultInfo == null) {
            throw new GatewayException(EnumGatewayException.USER_CORE_INVOKE_FAIL, "获取用户路由信息失败,用户编号:" + investorId);
        }
        return resultInfo;
    }

    /**
     * 查询用户分区号（优先从traceInfo取）
     *
     * @param investorId 用户编号
     * @return 分区号
     */
    public String getRouteZoneFromTraceInfo(String investorId) {
        String zone = null;
        TraceInfo traceInfo = TraceHelper.getTraceInfo();
        if (traceInfo != null) {
            zone = traceInfo.getZone();
        }
        if (StrUtil.isBlank(zone)) {
            RouteDTO routeDTO = getInvestorRoute(investorId);
            zone = String.valueOf(routeDTO.getZone());
        }
        return zone;
    }

    /**
     * 获取默认交易账户
     *
     * @param customerNo 用户编号
     * @return 交易账户实体
     */
    @Retryable(value = Exception.class, backoff = @Backoff(delay = 100L), exclude = GatewayException.class)
    @TraceMark
    public List<TradeAccountDTO> getDefaultTradeAccount(String customerNo) {
        String url = ZoneHelper.getUserCoreApiUrl(customerNo) + "/api/User/GetTradeInfoByTradeType";
        Map<String, String> paramMap = new HashMap<>(2);
        paramMap.put("CustomerNo", customerNo);
        paramMap.put("TradeAccountType", "000001");

        HttpEntity<Map<String, String>> request = GwBuildHelper.buildHttpEntity(paramMap, MediaType.APPLICATION_JSON, false);
        MidResultInfo<List<TradeAccountDTO>> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request, new ParameterizedTypeReference<MidResultInfo<List<TradeAccountDTO>>>() {
        }).getBody();
        if (midResultInfo == null || BooleanUtil.isFalse(midResultInfo.getSucceed())) {
            log.warn("获取用户默认交易账户失败：{}", midResultInfo);
            throw new GatewayException(EnumGatewayException.USER_CORE_INVOKE_FAIL);
        }
        if (CollectionUtils.isEmpty(midResultInfo.getResult()) || midResultInfo.getResult().size() == 0) {
            log.warn("当前用户无交易账户");
            throw new GatewayException(EnumGatewayException.SYSTEM_INTERNAL_ANOMALY);
        }
        return midResultInfo.getResult();
    }

    /**
     * 生成展示银行代码
     *
     * @param investorId 用户编号
     * @param bankCardNo 银行卡编号
     * @return
     */
    public String getShowBankCode(String investorId, String bankCardNo) {
        return BankCardHelper.getShowBankCode(getBankAccountByCardNo(investorId, bankCardNo, "000001"));
    }

    /**
     * 获取银行卡信息(可以查询到支付宝信息)
     *
     * @param customerNo 用户编号
     * @param bankCardNo 银行卡编号
     * @param channelNo  渠道编号
     * @return 银行账户实体
     */
    @Retryable(value = Exception.class, backoff = @Backoff(delay = 100L), exclude = GatewayException.class)
    @TraceMark
    @SentinelResource("core-user#getBankAccountByCardNo")
    @Cacheable(value = "getBankAccountByCardNo", cacheManager = "oneMinuteCaffeineCacheManager")
    public CustomerBankAcctDTO getBankAccountByCardNo(String customerNo, String bankCardNo, String channelNo) {
        String url = ZoneHelper.getUserCoreApiUrl(customerNo) + "/api/Bank/GetBankAccountByCardNo";
        Map<String, String> paramMap = new HashMap<>(4);
        paramMap.put("CustomerNo", customerNo);
        paramMap.put("BankCardNo", bankCardNo);
        paramMap.put("ChannelNo", channelNo);

        HttpEntity<Map<String, String>> request = GwBuildHelper.buildHttpEntity(paramMap, MediaType.APPLICATION_JSON, false);
        MidResultInfo<CustomerBankAcctDTO> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request, new ParameterizedTypeReference<MidResultInfo<CustomerBankAcctDTO>>() {
        }).getBody();
        // 接口返回异常 重试
        if (midResultInfo == null || !BooleanUtil.isTrue(midResultInfo.getSucceed())) {
            log.warn("获取银行账户信息（支付宝）失败：{}", midResultInfo);
            throw new GatewayException(EnumGatewayException.USER_CORE_INVOKE_FAIL);
        }
        return midResultInfo.getResult();
    }

    /**
     * 获取银行卡账号信息
     *
     * @param investorId    用户编号
     * @param bankAccountNo 银行账户编号
     * @return BankAccount BankAccountDTO
     */
    @Retryable(value = Exception.class, backoff = @Backoff(delay = 100L), exclude = GatewayException.class)
    @TraceMark
    @Cacheable(value = "getBankAccountDTO", cacheManager = "oneMinuteCaffeineCacheManager")
    public CustomerBankAcctDTO getBankAccountDTO(String investorId, String bankAccountNo) {
        String url = ZoneHelper.getUserCoreApiUrl(investorId) + "/api/Bank/GetBankAccount";
        Map<String, String> paramMap = new HashMap<>(2, 1);
        paramMap.put("CustomerNo", investorId);
        paramMap.put("BankAccountNo", bankAccountNo);

        HttpEntity<Map<String, String>> request = GwBuildHelper.buildHttpEntity(paramMap, MediaType.APPLICATION_JSON, false);
        MidResultInfo<CustomerBankAcctDTO> resultInfo = restTemplate.exchange(url, HttpMethod.POST, request, new ParameterizedTypeReference<MidResultInfo<CustomerBankAcctDTO>>() {
        }).getBody();

        return CommonHelper.resolveUserCoreResultInfo(resultInfo, false);
    }

    /**
     * 获取用户信息（证件/姓名等)
     *
     * @param investorId 用户编号
     * @return CustomerDTO 用户信息
     */
    @Retryable(Exception.class)
    @Cacheable(value = "getCustomerDTO", cacheManager = "oneMinuteCaffeineCacheManager")
    public CustomerDTO getCustomerDTO(String investorId) {
        String url = ZoneHelper.getUserCoreApiUrl(investorId) + "/api/User/GetCustomer";
        Map<String, String> paramMap = new HashMap<>(1, 1);
        paramMap.put("CustomerNo", investorId);

        HttpEntity<Map<String, String>> request = GwBuildHelper.buildHttpEntity(paramMap, MediaType.APPLICATION_JSON, false);
        MidResultInfo<CustomerDTO> resultInfo = restTemplate.exchange(url, HttpMethod.POST, request, new ParameterizedTypeReference<MidResultInfo<CustomerDTO>>() {
        }).getBody();
        return CommonHelper.resolveUserCoreResultInfo(resultInfo, true);
    }

    /**
     * 获取用户详细信息(手机/性别/邮件等）
     *
     * @param investorId 用户编号
     * @return CustomerDetailDTO 用户详细信息
     */
    @Retryable(value = Exception.class, backoff = @Backoff(delay = 100L), exclude = GatewayException.class)
    @TraceMark
    @Cacheable(value = "getCustomerDetailDTO", cacheManager = "durableCacheManager")
    public CustomerDetailDTO getCustomerDetailDTO(String investorId) {
        String url = ZoneHelper.getUserCoreApiUrl(investorId) + "/api/User/GetCustomerDetail";
        Map<String, String> paramMap = new HashMap<>(1, 1);
        paramMap.put("CustomerNo", investorId);

        HttpEntity<Map<String, String>> request = GwBuildHelper.buildHttpEntity(paramMap, MediaType.APPLICATION_JSON, false);
        MidResultInfo<CustomerDetailDTO> resultInfo = restTemplate.exchange(url, HttpMethod.POST, request, new ParameterizedTypeReference<MidResultInfo<CustomerDetailDTO>>() {
        }).getBody();
        return CommonHelper.resolveUserCoreResultInfo(resultInfo, true);
    }

    /**
     * 获取密文手机号(包含是否开通短信通知)
     *
     * @param investorId 用户编号
     * @return 手机号
     */
    @Retryable(value = Exception.class)
    @Cacheable(value = "getCustomerSecuritySetting", cacheManager = "oneMinuteCaffeineCacheManager")
    @TraceMark
    public CustomerSecurityDTO getCustomerSecuritySetting(String investorId) {
        String url = ZoneHelper.getUserCoreApiUrl(investorId) + "/api/User/GetCustSecuritySetting";
        Map<String, String> paramMap = new HashMap<>(1, 1);
        paramMap.put("CustomerNo", investorId);

        HttpEntity<Map<String, String>> request = GwBuildHelper.buildHttpEntity(paramMap, MediaType.APPLICATION_JSON, false);
        MidResultInfo<CustomerSecurityDTO> resultInfo = restTemplate.exchange(url, HttpMethod.POST, request
                , new ParameterizedTypeReference<MidResultInfo<CustomerSecurityDTO>>() {
                }).getBody();

        return CommonHelper.resolveUserCoreResultInfo(resultInfo, true);
    }
}