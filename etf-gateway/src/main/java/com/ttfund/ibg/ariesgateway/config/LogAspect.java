package com.ttfund.ibg.ariesgateway.config;


import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.csp.sentinel.slots.block.authority.AuthorityException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
import com.ttfund.ibg.gateway.common.constant.LogConstant;
import com.ttfund.ibg.gateway.common.exception.GatewayException;
import com.ttfund.ibg.gateway.common.exception.IbgException;
import com.ttfund.ibg.gateway.common.model.alarm.AlarmMessage;
import com.ttfund.ibg.gateway.common.model.alarm.EnumMessageType;
import com.ttfund.ibg.gateway.common.model.enums.EnumGatewayException;
import com.ttfund.ibg.gateway.common.model.enums.EnumLogType;
import com.ttfund.ibg.gateway.common.model.response.ErrorCodeConfigDTO;
import com.ttfund.ibg.gateway.common.model.response.ErrorCodeDetailDTO;
import com.ttfund.ibg.gateway.common.model.response.IbgResultInfo;
import com.ttfund.ibg.gateway.common.util.GwDateUtils;
import com.ttfund.ibg.gateway.core.config.GwFactory;
import com.ttfund.ibg.gateway.core.context.InvokeContext;
import com.ttfund.ibg.gateway.core.context.TraceHelper;
import com.ttfund.ibg.gateway.core.context.TraceInfo;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.marker.Markers;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;


import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/10 14:46
 */
@Aspect
@Slf4j
@Order(-1)
public class LogAspect {

    @Pointcut("execution(public * com.ttfund.ibg.*.controller..*.*(..))")
    public void aop() {

    }

    @Around("aop()")
    public Object around(ProceedingJoinPoint joinPoint) {
        TraceInfo traceInfo = null;
        long elapsedTime = 0;
        Object result;
        LocalDateTime endTime;

        try {
            // 初始化链路追踪信息
            traceInfo = TraceHelper.initTraceInfo(joinPoint);
            // 请求校验责任链
            GwFactory.getCheckSlotChain().entry(joinPoint);
            // 执行请求方法
            result = joinPoint.proceed();
            // 填充mdc日志必需字段
            TraceHelper.fillMdc(traceInfo, result);
            endTime = LocalDateTime.now();
            MDC.put(LogConstant.END_TIME, GwDateUtils.localDateTimeToIsoOffset(endTime));
            elapsedTime = LocalDateTimeUtil.between(traceInfo.getBeginTime(), endTime, ChronoUnit.MILLIS);
            log.info(Markers.append(LogConstant.REQUEST_ELAPSED, elapsedTime)
                    .and(Markers.append(LogConstant.LOG_TYPE, EnumLogType.InvokeAndReturn)), "业务成功");
        } catch (IbgException ibgException) {
            TraceHelper.finish(ibgException);
            result = resolveIbgException(ibgException);
            TraceHelper.fillMdc(traceInfo, result);
            endTime = LocalDateTime.now();
            MDC.put(LogConstant.END_TIME, GwDateUtils.localDateTimeToIsoOffset(endTime));
            if (traceInfo != null) {
                elapsedTime = LocalDateTimeUtil.between(traceInfo.getBeginTime(), endTime, ChronoUnit.MILLIS);
            }
            log.warn(Markers.append(LogConstant.REQUEST_ELAPSED, elapsedTime)
                    .and(Markers.append(LogConstant.LOG_TYPE, EnumLogType.InvokeAndReturn)), "ibg业务失败", ibgException);
        } catch (GatewayException gatewayException) {
            TraceHelper.finish(gatewayException);
            result = resolveGwException(gatewayException);
            TraceHelper.fillMdc(traceInfo, result);
            endTime = LocalDateTime.now();
            MDC.put(LogConstant.END_TIME, GwDateUtils.localDateTimeToIsoOffset(endTime));
            if (traceInfo != null) {
                elapsedTime = LocalDateTimeUtil.between(traceInfo.getBeginTime(), endTime, ChronoUnit.MILLIS);
            }
            log.warn(Markers.append(LogConstant.REQUEST_ELAPSED, elapsedTime)
                    .and(Markers.append(LogConstant.LOG_TYPE, EnumLogType.InvokeAndReturn)), "业务失败", gatewayException);
        } catch (Throwable throwable) {
            TraceHelper.finish(throwable);
            result = resolveSentinelException(throwable);
            TraceHelper.fillMdc(traceInfo, result);
            endTime = LocalDateTime.now();
            MDC.put(LogConstant.END_TIME, GwDateUtils.localDateTimeToIsoOffset(endTime));
            if (traceInfo != null) {
                elapsedTime = LocalDateTimeUtil.between(traceInfo.getBeginTime(), endTime, ChronoUnit.MILLIS);
            }
            log.error(Markers.append(LogConstant.REQUEST_ELAPSED, elapsedTime)
                    .and(Markers.append(LogConstant.LOG_TYPE, EnumLogType.InvokeError)), "业务异常", throwable);
        } finally {
            MDC.clear();
            InvokeContext.removeContext();
        }
        return result;
    }

    /**
     * 解析互联网业务服务异常
     *
     * @param ibgException 互联网业务服务异常
     * @return ResultInfo<?>
     */
    private IbgResultInfo<?> resolveIbgException(IbgException ibgException) {
        ErrorCodeDetailDTO detailDTO = GwFactory.getCommonManager().getErrorCodeDetail(ibgException.getBizCode(), ibgException.getBizErrorCode());
        if (Objects.equals(detailDTO.getNeedAlarm(), true)) {
            AlarmMessage alarmMessage = AlarmMessage.builder()
                    .resourceName(TraceHelper.getTraceInfo().getResourceName())
                    .errorMessage(ibgException.getBizErrorMessage())
                    .messageType(EnumMessageType.SPECIFIC_ERROR_CODE)
                    .build();
            // 异步报警 fail safe
            GwFactory.getCommonManager().alarm(alarmMessage);
        }

        Integer errorCode = detailDTO.getErrorCode();
        String errorMessage = Objects.equals(detailDTO.getUseInnerMessage(), true) ? ibgException.getBizErrorMessage() : detailDTO.getMessage();
        return new IbgResultInfo<>().failed(errorCode, errorMessage);
    }

    /**
     * 解析业务异常
     *
     * @param gatewayException 网关异常
     * @return ResultInfo<?>
     */
    private IbgResultInfo<?> resolveGwException(GatewayException gatewayException) {
        Integer errorCode;
        String errorMessage;
        boolean hasWrongToken = false;
        if (gatewayException.getMessage() != null) {
            errorCode = gatewayException.getErrorCode();
            errorMessage = gatewayException.getMessage();
            hasWrongToken = BooleanUtil.isTrue(gatewayException.getHasWrongToken());
        } else {
            ErrorCodeConfigDTO errorCodeConfigDTO = GwFactory.getCommonManager().getErrorCodeConfig(gatewayException.getErrorCode());
            errorCode = errorCodeConfigDTO.getErrorCode();
            errorMessage = errorCodeConfigDTO.getMessage();
        }

        return new IbgResultInfo<>().failed(errorCode, errorMessage, hasWrongToken);
    }

    /**
     * 解析抛出的异常是否为sentinel相关
     *
     * @param throwable 异常
     */
    private IbgResultInfo<?> resolveSentinelException(Throwable throwable) {
        ErrorCodeConfigDTO errorCodeConfigDTO = GwFactory.getCommonManager().getErrorCodeConfig(EnumGatewayException.SYSTEM_INTERNAL_ANOMALY.getErrorCode());
        IbgResultInfo<?> resultInfo = new IbgResultInfo<>().failed(errorCodeConfigDTO);
        if (throwable.getCause() == null) {
            return resultInfo;
        }

        if (AuthorityException.class.isAssignableFrom(throwable.getCause().getClass())) {
            errorCodeConfigDTO = GwFactory.getCommonManager().getErrorCodeConfig(EnumGatewayException.AUTH_EXCEPTION.getErrorCode());
            resultInfo.failed(errorCodeConfigDTO);
        } else if (FlowException.class.isAssignableFrom(throwable.getCause().getClass())) {
            errorCodeConfigDTO = GwFactory.getCommonManager().getErrorCodeConfig(EnumGatewayException.FLOW_EXCEPTION.getErrorCode());
            resultInfo.failed(errorCodeConfigDTO);
        } else if (DegradeException.class.isAssignableFrom(throwable.getCause().getClass())) {
            errorCodeConfigDTO = GwFactory.getCommonManager().getErrorCodeConfig(EnumGatewayException.DEGRADE_EXCEPTION.getErrorCode());
            resultInfo.failed(errorCodeConfigDTO);
        }

        return resultInfo;
    }
}
