package com.ttfund.ibg.ariesgateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 监控服务禁用配置
 *
 * <AUTHOR>
 * @date 2025/07/31
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "monitor.enabled", havingValue = "false", matchIfMissing = true)
public class MonitorDisableConfig {

    @PostConstruct
    public void init() {
        log.info("基金通监控服务已禁用 - 监控相关功能将不会启动");

        // 设置系统属性来禁用监控相关功能
        System.setProperty("alarm.rule.sync.enabled", "false");
        System.setProperty("monitor.enabled", "false");
        System.setProperty("csp.sentinel.config.apollo.enabled", "false");
        System.setProperty("csp.sentinel.apollo.rule.enabled", "false");

        log.info("已设置系统属性禁用监控功能和 Sentinel Apollo 配置");
    }
}
