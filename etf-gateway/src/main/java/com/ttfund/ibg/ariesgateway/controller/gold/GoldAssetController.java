package com.ttfund.ibg.ariesgateway.controller.gold;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.ttfund.ibg.gateway.common.model.response.IbgResultInfo;
import com.ttfund.ibg.gateway.goldcommon.model.request.*;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.*;
import com.ttfund.ibg.gateway.goldcommon.service.GoldAssetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 积存金：资产 收益查询
 *
 * <AUTHOR>
 * @date 2025/3/12
 */
@Api(value = "积存金：资产 收益查询controller", tags = "积存金：资产 收益查询controller")
@Slf4j
@RestController
@RequestMapping("/gold/asset")
public class GoldAssetController {

    @Resource
    GoldAssetService goldAssetService;

    @ApiOperation(value = "持仓摘要")
    @PostMapping(value = "/getHoldShareSummary")
    @SentinelResource("aries-gw#getHoldShareSummary")
    public IbgResultInfo<HoldShareSummaryVO> getHoldShareSummary(@RequestBody HoleShareSummaryParam param) {
        return new IbgResultInfo<HoldShareSummaryVO>().succeed(goldAssetService.getHoldShareSummary(param));
    }

    @ApiOperation(value = "总收益/总收益率")
    @PostMapping(value = "getTotalProfitInfo")
    @SentinelResource("aries-gw#getTotalProfitInfo")
    public IbgResultInfo<TotalProfitInfoVO> getTotalProfitInfo(@RequestBody TotalProfitInfoParam param) {
        return new IbgResultInfo<TotalProfitInfoVO>().succeed(goldAssetService.getTotalProfitInfo(param));
    }

    @ApiOperation(value = "区间资产信息")
    @PostMapping(value = "getPeriodAsset")
    @SentinelResource("aries-gw#getPeriodAsset")
    public IbgResultInfo<PeriodAssetVO> getPeriodAsset(@RequestBody PeriodAssetParam param) {
        return new IbgResultInfo<PeriodAssetVO>().succeed(goldAssetService.getPeriodAsset(param));
    }

    @ApiOperation(value = "收益趋势图")
    @PostMapping(value = "getProfitTrendChart")
    @SentinelResource("aries-gw#getProfitTrendChart")
    public IbgResultInfo<List<ProfitTrendChartVO>> getProfitTrendChart(@RequestBody ProfitTrendChartParam param) {
        return new IbgResultInfo<List<ProfitTrendChartVO>>().succeed(goldAssetService.getProfitTrendChart(param));
    }

    @ApiOperation(value = "收益日历图")
    @PostMapping(value = "getProfitCalendarChart")
    @SentinelResource("aries-gw#getProfitCalendarChart")
    public IbgResultInfo<List<ChartEntityVO>> getProfitCalendarChart(@RequestBody ProfitCalendarChartParam param) {
        return new IbgResultInfo<List<ChartEntityVO>>().succeed(goldAssetService.getProfitCalendarChart(param));
    }

}
