package com.ttfund.ibg.ariesgateway.service.gold;

import com.ttfund.ibg.ariesgateway.manager.GoldManager;
import com.ttfund.ibg.ariesgateway.util.GoldBuilderHelper;
import com.ttfund.ibg.gateway.goldcommon.model.request.*;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.*;
import com.ttfund.ibg.gateway.goldcommon.service.GoldAssetService;
import com.ttfund.ibg.pisces.entity.resp.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 积存金：资产 收益查询
 *
 * <AUTHOR>
 * @date 2025/3/12
 */
@Slf4j
@Service
public class GoldAssetServiceImpl implements GoldAssetService {

    @Resource
    private GoldManager goldManager;

    /**
     * 持仓摘要
     *
     * @param param 入参
     * @return 持仓
     */
    @Override
    public HoldShareSummaryVO getHoldShareSummary(HoleShareSummaryParam param) {
        HoldShareSummary dtoHoldShareSummary = goldManager.getHoldShareSummary(param.getGoldBankCode(), param.getInvestorId());
        return GoldBuilderHelper.buildHoldShareSummary(dtoHoldShareSummary);
    }

    /**
     * 总收益/总收益率
     *
     * @param param
     * @return
     */
    @Override
    public TotalProfitInfoVO getTotalProfitInfo(TotalProfitInfoParam param) {
        TotalProfitInfo dtoTotalProfitInfo = goldManager.getTotalProfitInfo(param.getGoldBankCode(), param.getInvestorId());
        return GoldBuilderHelper.buildTotalProfitInfo(dtoTotalProfitInfo);
    }

    /**
     * 区间资产信息
     *
     * @param param
     * @return
     */
    @Override
    public PeriodAssetVO getPeriodAsset(PeriodAssetParam param) {
        PeriodAsset dtoPeriodAsset = goldManager.getPeriodAsset(param.getGoldBankCode(), param.getInvestorId()
                , param.getPeriod(), param.getType());
        return GoldBuilderHelper.buildPeriodAsset(dtoPeriodAsset);
    }

    /**
     * 收益趋势图
     *
     * @param param
     * @return
     */
    @Override
    public List<ProfitTrendChartVO> getProfitTrendChart(ProfitTrendChartParam param) {
        ProfitTrendChart dtoProfitTrendChart = goldManager.getProfitTrendChart(param.getGoldBankCode(), param.getInvestorId()
                , param.getBeginDate(), param.getEndDate(), param.getMaxPointCount(), param.getType());
        return GoldBuilderHelper.buildProfitTrendChart(dtoProfitTrendChart);
    }

    /**
     * 收益日历图
     *
     * @param param
     * @return
     */
    @Override
    public List<ChartEntityVO> getProfitCalendarChart(ProfitCalendarChartParam param) {
        ProfitCalendarChart dtoProfitCalendarChart = goldManager.getProfitCalendarChart(param.getGoldBankCode()
                , param.getInvestorId(), param.getBeginDate(), param.getEndDate(), param.getType());
        return GoldBuilderHelper.buildProfitCalendarChart(dtoProfitCalendarChart);
    }
}
