package com.ttfund.ibg.ariesgateway.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * 本地缓存配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/27 10:17
 */
@Configuration
public class CacheConfig {

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Primary
    @Bean(name = "caffeineCacheManager")
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(10, TimeUnit.SECONDS)
                .initialCapacity(10000)
                .maximumSize(100000));
        return cacheManager;
    }

    @Bean(name = "durableCacheManager")
    public CacheManager durableCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(10, TimeUnit.MINUTES)
                .initialCapacity(1024)
                .maximumSize(10000));
        return cacheManager;
    }

    @Bean(name = "moderateCacheManager")
    public CacheManager moderateCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(5, TimeUnit.MINUTES)
                .initialCapacity(1024)
                .maximumSize(10000));
        return cacheManager;
    }

    @Bean(name = "oneMinuteCaffeineCacheManager")
    public CacheManager oneMinuteCaffeineCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.MINUTES)
                .initialCapacity(1024)
                .maximumSize(10000));
        return cacheManager;
    }


}
