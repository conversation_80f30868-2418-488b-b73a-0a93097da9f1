package com.ttfund.ibg.ariesgateway.controller.gold;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.ttfund.ibg.gateway.common.annotation.AntiMoneyLaunder;
import com.ttfund.ibg.gateway.common.annotation.PasswordVerify;
import com.ttfund.ibg.gateway.common.model.enums.EnumPwdVerify;
import com.ttfund.ibg.gateway.common.model.response.IbgResultInfo;
import com.ttfund.ibg.gateway.goldcommon.model.request.*;
import com.ttfund.ibg.gateway.goldcommon.model.response.vo.*;
import com.ttfund.ibg.gateway.goldcommon.service.GoldTradeService;
import com.ttfund.ibg.pisces.entity.PaginationData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 积存金：充值 申购 提现
 *
 * <AUTHOR>
 * @date 2025/3/12
 */
@Api(value = "积存金：充值 申购 提现controller", tags = "积存金：充值 申购 提现controller")
@Slf4j
@RestController
@RequestMapping("/gold/trade")
public class GoldTradeController {

    @Resource
    GoldTradeService goldTradeService;

    @ApiOperation(value = "银行通知（系统维护信息等）")
    @PostMapping(value = "/getBankNotice")
    @SentinelResource("aries-gw#goldBankNotice")
    public IbgResultInfo<List<BankNoticeVO>> getBankNotice(@RequestBody BankNoticeParam param) {
        return new IbgResultInfo<List<BankNoticeVO>>().succeed(goldTradeService.getBankNotice(param));
    }

    @ApiOperation(value = "黄金市场交易状态:开盘时间等")
    @PostMapping(value = "/getMarketInfo")
    @SentinelResource("aries-gw#goldMarketInfo")
    public IbgResultInfo<MarketInfoVO> getMarketInfo(@RequestBody MarketInfoParam param) {
        return new IbgResultInfo<MarketInfoVO>().succeed(goldTradeService.getMarketInfo(param));
    }

    @ApiOperation(value = "入金预申请")
    @PostMapping(value = "/cashInPre")
    @SentinelResource("aries-gw#cashInPre")
    @AntiMoneyLaunder
    // todo:ly
//    @PasswordVerify(value = EnumPwdVerify.PASSWORD)
    public IbgResultInfo<CashInPreVO> cashInPre(@RequestBody CashInPreParam param) {
        return new IbgResultInfo<CashInPreVO>().succeed(goldTradeService.cashInPre(param));
    }

    @ApiOperation(value = "入金")
    @PostMapping(value = "/cashIn")
    @SentinelResource("aries-gw#cashIn")
    public IbgResultInfo<CashInVO> cashIn(@RequestBody CashInParam param) {
        return new IbgResultInfo<CashInVO>().succeed(goldTradeService.cashIn(param));
    }

    @ApiOperation(value = "出金预申请")
    @PostMapping(value = "/cashOutPre")
    @SentinelResource("aries-gw#cashOutPre")
    // todo:ly
//    @PasswordVerify(value = EnumPwdVerify.PASSWORD)
    public IbgResultInfo<CashOutPreVO> cashOutPre(@RequestBody CashOutPreParam param) {
        return new IbgResultInfo<CashOutPreVO>().succeed(goldTradeService.cashOutPre(param));
    }

    @ApiOperation(value = "出金")
    @PostMapping(value = "/cashOut")
    @SentinelResource("aries-gw#cashOut")
    public IbgResultInfo<CashOutVO> cashOut(@RequestBody CashOutParam param) {
        return new IbgResultInfo<CashOutVO>().succeed(goldTradeService.cashOut(param));
    }

    @ApiOperation(value = "买卖点")
    @PostMapping(value = "/getBuySellPoint")
    @SentinelResource("aries-gw#goldBuySellPoint")
    public IbgResultInfo<List<BuySellPointVO>> getBuySellPoint(@RequestBody BuySellPointParam param) {
        return new IbgResultInfo<List<BuySellPointVO>>().succeed(goldTradeService.getBuySellPoint(param));
    }

    @ApiOperation(value = "资金记录查询")
    @PostMapping(value = "/cashListQuery")
    @SentinelResource("aries-gw#cashListQuery")
    public IbgResultInfo<PaginationData<CashListQueryVO>> cashListQuery(@RequestBody CashListQueryParam param) {
        return new IbgResultInfo<PaginationData<CashListQueryVO>>().succeed(goldTradeService.cashListQuery(param));
    }

    @ApiOperation(value = "交易总览查询")
    @PostMapping(value = "/tradeSummaryQuery")
    @SentinelResource("aries-gw#goldTradeSummaryQuery")
    public IbgResultInfo<TradeSummaryVO> tradeSummaryQuery(@RequestBody TradeSummaryQueryParam param) {
        return new IbgResultInfo<TradeSummaryVO>().succeed(goldTradeService.tradeSummaryQuery(param));
    }

    @ApiOperation(value = "交易记录查询")
    @PostMapping(value = "/tradeListQuery")
    @SentinelResource("aries-gw#goldTradeListQuery")
    public IbgResultInfo<PaginationData<TradeInfoSimpleVO>> tradeListQuery(@RequestBody TradeListQueryParam param) {
        return new IbgResultInfo<PaginationData<TradeInfoSimpleVO>>().succeed(goldTradeService.tradeListQuery(param));
    }

    @ApiOperation(value = "交易详情查询")
    @PostMapping(value = "/tradeDetailQuery")
    @SentinelResource("aries-gw#goldTradeDetailQuery")
    public IbgResultInfo<TradeInfoDetailVO> tradeDetailQuery(@RequestBody TradeDetailQueryParam param) {
        return new IbgResultInfo<TradeInfoDetailVO>().succeed(goldTradeService.tradeDetailQuery(param));
    }

    @ApiOperation(value = "积存金申购")
    @PostMapping(value = "/purchase")
    @SentinelResource("aries-gw#purchase")
    @AntiMoneyLaunder
    // todo:ly
//    @PasswordVerify(value = EnumPwdVerify.PASSWORD)
    public IbgResultInfo<PurchaseVO> purchase(@RequestBody PurchaseParam param) {
        return new IbgResultInfo<PurchaseVO>().succeed(goldTradeService.purchase(param));
    }

    @ApiOperation(value = "积存金赎回")
    @PostMapping(value = "/redeem")
    @SentinelResource("aries-gw#redeem")
    // todo:ly
//    @PasswordVerify(value = EnumPwdVerify.PASSWORD)
    public IbgResultInfo<RedeemVO> redeem(@RequestBody RedeemParam param) {
        return new IbgResultInfo<RedeemVO>().succeed(goldTradeService.redeem(param));
    }


}
