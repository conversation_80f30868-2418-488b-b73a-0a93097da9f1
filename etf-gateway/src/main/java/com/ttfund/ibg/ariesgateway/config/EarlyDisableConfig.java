package com.ttfund.ibg.ariesgateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

/**
 * 早期禁用配置
 * 在应用启动的早期阶段禁用监控和 Sentinel 相关功能
 * 
 * <AUTHOR>
 * @date 2025/07/31
 */
@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
public class EarlyDisableConfig implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        log.info("早期禁用配置开始执行");
        
        // 禁用监控相关功能
        System.setProperty("alarm.rule.sync.enabled", "false");
        System.setProperty("monitor.enabled", "false");
        
        // 禁用 Sentinel Apollo 配置
        System.setProperty("csp.sentinel.config.apollo.enabled", "false");
        System.setProperty("csp.sentinel.apollo.rule.enabled", "false");
        
        // 禁用 Apollo 自动发现 namespace
        System.setProperty("apollo.autoUpdateInjectedSpringProperties", "false");
        
        log.info("早期禁用配置执行完成 - 已禁用监控和 Sentinel Apollo 配置");
    }
}
