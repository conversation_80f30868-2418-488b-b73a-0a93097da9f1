package com.ttfund.ibg.ariesgateway.manager;

import com.ttfund.ibg.aries.entity.ResultInfo;
import com.ttfund.ibg.ariesgateway.util.ZoneHelper;
import com.ttfund.ibg.gateway.common.annotation.TraceMark;
import com.ttfund.ibg.gateway.common.exception.GatewayException;
import com.ttfund.ibg.gateway.common.exception.IbgException;
import com.ttfund.ibg.gateway.common.model.enums.EnumGatewayException;
import com.ttfund.ibg.gateway.common.model.enums.EnumIbgBiz;
import com.ttfund.ibg.gateway.core.util.GwBuildHelper;
import com.ttfund.ibg.gateway.goldcommon.model.request.dto.MarketInfoDTO;
import com.ttfund.ibg.pisces.entity.PaginationData;
import com.ttfund.ibg.pisces.entity.req.*;
import com.ttfund.ibg.pisces.entity.resp.*;
import com.ttfund.ibg.pisces.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 积存金中台
 *
 * <AUTHOR>
 * @date 2025/3/12
 */
@Slf4j
@Component
public class GoldManager {

    @Resource
    private RestTemplate restTemplate;

    /**
     * 进度查询
     *
     * @param bankCode   二类户所在行(只查民生的，传民生的银行code，不传查所有的)
     * @param customerNo 用户编号
     * @param followType 流程类型
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    public ProcessQueryResp processQuery(String bankCode, String customerNo, String followType) {
        String url = ZoneHelper.getGoldApiUrl() + "/common/processQuery";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        paraMap.put("followType", EnumFollowType.getEnum(followType));
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<ProcessQueryResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<ProcessQueryResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, false);
    }

    /**
     * 身份证更新预申请
     *
     * @param bankCode   二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param customerNo 用户编号
     * @return
     */
    @TraceMark
    public UpdateIdCardPreResp updateIdCardPre(String bankCode, String customerNo) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/updateIdCardPre";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<UpdateIdCardPreResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<UpdateIdCardPreResp>>() {
                }).getBody();
        return resolveSpecialResultInfo(midResultInfo, true);
    }

    /**
     * 身份证更新申请
     *
     * @param updateIdCardReq
     * @return
     */
    @TraceMark
    public UpdateIdCardResp updateIdCard(UpdateIdCardReq updateIdCardReq) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/updateIdCard";
        HttpEntity<UpdateIdCardReq> request = GwBuildHelper.buildHttpEntity(updateIdCardReq, MediaType.APPLICATION_JSON, false);
        ResultInfo<UpdateIdCardResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<UpdateIdCardResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 开户进度查询
     *
     * @param bankCode   二类户所在行(只查民生的，传民生的银行code，不传查所有的)
     * @param customerNo 用户编号
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    public OpenAccountProcessResp openAccountProcessQuery(String bankCode, String customerNo) {
        String url = ZoneHelper.getGoldApiUrl() + "/account/openAccountProcessQuery";
        Map<String, Object> paraMap = new HashMap<>(2);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<OpenAccountProcessResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<OpenAccountProcessResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 账户信息查询
     *
     * @param bankCode   二类户所在行(只查民生的，传民生的银行code，不传查所有的)
     * @param customerNo 用户编号
     * @return 账户信息列表
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    public GoldAccountQueryResp goldAccountQuery(String bankCode, String customerNo) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/goldAccountQuery";
        Map<String, Object> paraMap = new HashMap<>(2);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<GoldAccountQueryResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<GoldAccountQueryResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, false);
    }

    /**
     * 绑定卡校验
     *
     * @param bankCode       二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param bindBankCardNo 绑定卡号
     * @param bindBankCode   绑定卡所属银行
     * @param customerNo     用户编号
     * @return 校验
     */
    @TraceMark
    public BindCardCheckResp bindCardCheck(String bankCode, String bindBankCardNo, String bindBankCode, String customerNo) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/bindCardCheck";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("bankCode", bankCode);
        paraMap.put("bindBankCardNo", bindBankCardNo);
        paraMap.put("bindBankCode", bindBankCode);
        paraMap.put("customerNo", customerNo);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<BindCardCheckResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<BindCardCheckResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 变更绑定卡预申请
     *
     * @param bankCode          二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param changeType        变更方式
     * @param customerNo        用户编号
     * @param holdImageId       手持照片Id
     * @param newBindBankCardNo 新绑定卡号
     * @param newBindBankCode   新绑定卡所属银行
     * @return
     */
    @TraceMark
    public ChangeBindCardPreResp changeBindCardPre(String bankCode, EnumFollowType changeType, String customerNo, String holdImageId
            , String newBindBankCardNo, String newBindBankCode) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/changeBindCardPre";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("bankCode", bankCode);
        paraMap.put("changeType", changeType);
        paraMap.put("customerNo", customerNo);
        paraMap.put("holdImageId", holdImageId);
        paraMap.put("newBindBankCardNo", newBindBankCardNo);
        paraMap.put("newBindBankCode", newBindBankCode);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<ChangeBindCardPreResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<ChangeBindCardPreResp>>() {
                }).getBody();
        return resolveSpecialResultInfo(midResultInfo, true);
    }

    /**
     * 变更绑定卡申请
     *
     * @param changeBindCardReq
     * @return
     */
    @TraceMark
    public ChangeBindCardResp changeBindCard(ChangeBindCardReq changeBindCardReq) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/changeBindCard";
        HttpEntity<ChangeBindCardReq> request = GwBuildHelper.buildHttpEntity(changeBindCardReq, MediaType.APPLICATION_JSON, false);
        ResultInfo<ChangeBindCardResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<ChangeBindCardResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 变更绑定卡确认
     *
     * @param bankCode   二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param cfmSmsCode 确认通过验证码
     * @param preApplyId 变更绑定卡预申请Id
     * @param customerNo 用户编号
     * @return 校验
     */
    @TraceMark
    public ChangeBindCardCfmResp changeBindCardConfirm(String bankCode, String cfmSmsCode, String preApplyId, String customerNo) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/changeBindCardCfm";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("bankCode", bankCode);
        paraMap.put("cfmSmsCode", cfmSmsCode);
        paraMap.put("preApplyId", preApplyId);
        paraMap.put("customerNo", customerNo);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<ChangeBindCardCfmResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<ChangeBindCardCfmResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 变更手机号预申请
     *
     * @param bankCode     二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param changeType   变更方式
     * @param customerNo   用户编号
     * @param holdImageId  手持照片Id
     * @param mobileTelEncrypt  是否加密
     * @param newMobileTel 新手机号
     * @return
     */
    @TraceMark
    public ChangeMobilePreResp changeMobilePre(String bankCode, EnumFollowType changeType, String customerNo
            , String holdImageId, Boolean mobileTelEncrypt, String newMobileTel) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/changeMobilePre";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("bankCode", bankCode);
        paraMap.put("changeType", changeType);
        paraMap.put("customerNo", customerNo);
        paraMap.put("holdImageId", holdImageId);
        paraMap.put("mobileTelEncrypt", mobileTelEncrypt);
        paraMap.put("newMobileTel", newMobileTel);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<ChangeMobilePreResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<ChangeMobilePreResp>>() {
                }).getBody();
        return resolveSpecialResultInfo(midResultInfo, true);
    }

    /**
     * 变更手机号申请
     *
     * @param changeMobileReq
     * @return
     */
    @TraceMark
    public ChangeMobileResp changeMobile(ChangeMobileReq changeMobileReq) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/changeMobile";
        HttpEntity<ChangeMobileReq> request = GwBuildHelper.buildHttpEntity(changeMobileReq, MediaType.APPLICATION_JSON, false);
        ResultInfo<ChangeMobileResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<ChangeMobileResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 账户解锁预申请
     *
     * @param bankCode   二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param customerNo 用户编号
     * @return
     */
    @TraceMark
    public AccountUnLockPreResp unLockAccountPre(String bankCode, String customerNo) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/unLockAccountPre";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<AccountUnLockPreResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<AccountUnLockPreResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 账户解锁申请
     *
     * @param accountUnLockReq
     * @return
     */
    @TraceMark
    public AccountUnLockResp unLockAccount(AccountUnLockReq accountUnLockReq) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/unLockAccount";
        HttpEntity<AccountUnLockReq> request = GwBuildHelper.buildHttpEntity(accountUnLockReq, MediaType.APPLICATION_JSON, false);
        ResultInfo<AccountUnLockResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<AccountUnLockResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 账户注销预申请
     *
     * @param bankCode   二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param customerNo 用户编号
     * @return
     */
    @TraceMark
    public DestroyAccountPreResp destroyAccountPre(String bankCode, String customerNo) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/destroyAccountPre";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<DestroyAccountPreResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<DestroyAccountPreResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 账户注销申请
     *
     * @param destroyAccountReq
     * @return
     */
    @TraceMark
    public DestroyAccountResp destroyAccount(DestroyAccountReq destroyAccountReq) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/destroyAccount";
        HttpEntity<DestroyAccountReq> request = GwBuildHelper.buildHttpEntity(destroyAccountReq, MediaType.APPLICATION_JSON, false);
        ResultInfo<DestroyAccountResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<DestroyAccountResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 获取二类卡余额
     *
     * @param bankCode   二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param customerNo 用户编号
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    @Cacheable(value = "goldBalanceQuery", cacheManager = "caffeineCacheManager")
    public BalanceQueryResp balanceQuery(String bankCode, String customerNo) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/balanceQuery";
        Map<String, Object> paraMap = new HashMap<>(2);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<BalanceQueryResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<BalanceQueryResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, false);
    }

    /**
     * 区县代码查询
     *
     * @param bankCode 二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    @Cacheable(value = "goldAreaList", cacheManager = "durableCacheManager")
    public List<AreaInfo> getAreaList(String bankCode) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/getAreaList?bankCode={1}";
        ResultInfo<List<AreaInfo>> midResultInfo = restTemplate.exchange(url, HttpMethod.GET, null,
                new ParameterizedTypeReference<ResultInfo<List<AreaInfo>>>() {
                }, bankCode).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 职业代码查询
     *
     * @param bankCode 二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    @Cacheable(value = "goldJobList", cacheManager = "durableCacheManager")
    public List<JobInfo> getJobList(String bankCode) {
        String url = ZoneHelper.getGoldApiUrl() + "/accountManager/getJobList?bankCode={1}";
        ResultInfo<List<JobInfo>> midResultInfo = restTemplate.exchange(url, HttpMethod.GET, null,
                new ParameterizedTypeReference<ResultInfo<List<JobInfo>>>() {
                }, bankCode).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 支持的银行列表
     *
     * @param bankCode 二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    @Cacheable(value = "getBankConfig", cacheManager = "oneMinuteCaffeineCacheManager")
    public BankConfig getBankConfig(String bankCode) {
        String url = ZoneHelper.getGoldApiUrl() + "/common/getBankConfig?bankCode={1}";
        ResultInfo<BankConfig> midResultInfo = restTemplate.exchange(url, HttpMethod.GET, null,
                new ParameterizedTypeReference<ResultInfo<BankConfig>>() {
                }, bankCode).getBody();
        return resolveResultInfo(midResultInfo, false);
    }

    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    public RiskQuestionQryResp riskQuery(RiskQuestionQryReq req) {
        String url = ZoneHelper.getGoldApiUrl() + "/account/riskQuery";

        HttpEntity<RiskQuestionQryReq> request = GwBuildHelper.buildHttpEntity(req, MediaType.APPLICATION_JSON, false);
        ResultInfo<RiskQuestionQryResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<RiskQuestionQryResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    @TraceMark
    public RiskResultCommitResp riskCommit(RiskResultCommitReq req) {
        String url = ZoneHelper.getGoldApiUrl() + "/account/riskCommit";

        HttpEntity<RiskResultCommitReq> request = GwBuildHelper.buildHttpEntity(req, MediaType.APPLICATION_JSON, false);
        ResultInfo<RiskResultCommitResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<RiskResultCommitResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 开卡预申请
     *
     * @param customerNo       用户编号
     * @param bankCode         二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param bindBankCardNo   绑定卡号
     * @param bindBankCode     绑定卡所属银行
     * @param mobileTel        二类户预留手机号
     * @param mobileTelEncrypt 手机号是否加密
     * @param districtCode     区县代码(民生必传)
     * @param detailAddress    街道详细地址(民生必传)
     * @param jobCode          职业代码(民生必传)
     * @param jobRemark        职业备注(民生必传且jobCode为82时，该字段必传，且只能传‘父母资助、他人捐赠、个人投资、个体经营’中的一个)
     * @param company          工作单位(民生必传且jobCode为80、81、82时，该字段只能传‘无’，其他情况不可填无)
     * @param faceImageId      人脸照片id
     * @return
     */
    @TraceMark
    public OpenAccountPreResp openCardPre(String customerNo, String bankCode, String bindBankCardNo, String bindBankCode,
                                          String mobileTel, Boolean mobileTelEncrypt, String districtCode, String detailAddress, String jobCode,
                                          String jobRemark, String company, String faceImageId) {
        String url = ZoneHelper.getGoldApiUrl() + "/account/openCardPre";
        Map<String, Object> paraMap = new HashMap<>(16);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        paraMap.put("bindBankCardNo", bindBankCardNo);
        paraMap.put("bindBankCode", bindBankCode);
        paraMap.put("mobileTel", mobileTel);
        paraMap.put("mobileTelEncrypt", mobileTelEncrypt);
        paraMap.put("districtCode", districtCode);
        paraMap.put("detailAddress", detailAddress);
        paraMap.put("jobCode", jobCode);
        paraMap.put("jobRemark", jobRemark);
        paraMap.put("company", company);
        paraMap.put("faceImageId", faceImageId);

        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<OpenAccountPreResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<OpenAccountPreResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 开卡申请
     *
     * @param customerNo
     * @param bankCode
     * @param preApplyId
     * @param smsCode
     * @param smsId
     * @return
     */
    @TraceMark
    public OpenAccountResp openCard(String customerNo, String bankCode, String preApplyId, String smsCode, String smsId) {
        String url = ZoneHelper.getGoldApiUrl() + "/account/openCard";
        Map<String, Object> paraMap = new HashMap<>(8);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        paraMap.put("preApplyId", preApplyId);
        SmsInfo smsInfo = new SmsInfo();
        smsInfo.setSmsCode(smsCode);
        smsInfo.setSmsId(smsId);
        paraMap.put("smsInfo", smsInfo);

        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<OpenAccountResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<OpenAccountResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 入金签约预申请
     *
     * @param customerNo
     * @param bankCode
     * @return
     */
    @TraceMark
    public CashInSignPreResp cashInSignPre(String customerNo, String bankCode, String cashInChannel) {
        String url = ZoneHelper.getGoldApiUrl() + "/account/cashInSignPre";
        Map<String, Object> paraMap = new HashMap<>(4);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        paraMap.put("cashInChannel", cashInChannel);

        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<CashInSignPreResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<CashInSignPreResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 入金签约申请
     *
     * @param customerNo
     * @param bankCode
     * @param preApplyId
     * @param smsCode
     * @param smsId
     * @return
     */
    @TraceMark
    public CashInSignResp cashInSign(String customerNo, String bankCode, String preApplyId, String smsCode, String smsId) {
        String url = ZoneHelper.getGoldApiUrl() + "/account/cashInSign";
        Map<String, Object> paraMap = new HashMap<>(8);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        paraMap.put("preApplyId", preApplyId);
        SmsInfo smsInfo = new SmsInfo();
        smsInfo.setSmsCode(smsCode);
        smsInfo.setSmsId(smsId);
        paraMap.put("smsInfo", smsInfo);

        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<CashInSignResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<CashInSignResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 积存金签约预申请
     *
     * @param customerNo
     * @param bankCode
     * @return
     */
    @TraceMark
    public GoldSignPreResp goldSignPre(String customerNo, String bankCode) {
        String url = ZoneHelper.getGoldApiUrl() + "/account/goldSignPre";
        Map<String, Object> paraMap = new HashMap<>(4);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);

        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<GoldSignPreResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<GoldSignPreResp>>() {
                }).getBody();
        return resolveSpecialResultInfo(midResultInfo, true);
    }

    /**
     * 积存金签约申请
     *
     * @param customerNo
     * @param bankCode
     * @param preApplyId
     * @param smsCode
     * @param smsId
     * @return
     */
    @TraceMark
    public GoldSignResp goldSign(String customerNo, String bankCode, String preApplyId, String smsCode, String smsId) {
        String url = ZoneHelper.getGoldApiUrl() + "/account/goldSign";
        Map<String, Object> paraMap = new HashMap<>(8);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        paraMap.put("preApplyId", preApplyId);
        SmsInfo smsInfo = new SmsInfo();
        smsInfo.setSmsCode(smsCode);
        smsInfo.setSmsId(smsId);
        paraMap.put("smsInfo", smsInfo);

        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<GoldSignResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<GoldSignResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 银行通知（系统维护信息等）
     *
     * @param bankCode 二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    @Cacheable(value = "goldBankNotice", cacheManager = "oneMinuteCaffeineCacheManager")
    public List<BankNotice> getBankNotice(String bankCode) {
        String url = ZoneHelper.getGoldApiUrl() + "/common/getBankNotice?bankCode={1}";
        ResultInfo<List<BankNotice>> midResultInfo = restTemplate.exchange(url, HttpMethod.GET, null,
                new ParameterizedTypeReference<ResultInfo<List<BankNotice>>>() {
                }, bankCode).getBody();
        return resolveResultInfo(midResultInfo, false);
    }

    /**
     * 黄金市场交易状态:开盘时间等
     *
     * @param bankCode 二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    public MarketInfoDTO getMarketInfo(String bankCode) {
        String url = ZoneHelper.getGoldApiUrl() + "/common/getMarketInfo?bankCode={1}";
        ResultInfo<MarketInfoDTO> midResultInfo = restTemplate.exchange(url, HttpMethod.GET, null,
                new ParameterizedTypeReference<ResultInfo<MarketInfoDTO>>() {
                }, bankCode).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 买卖点
     *
     * @param bankCode   二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param customerNo 用户编号
     * @param beginDate  查询开始时间
     * @param endDate    查询结束时间
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    @Cacheable(value = "goldBuySellPoint", cacheManager = "caffeineCacheManager")
    public GetBuySellPointResp getBuySellPoint(String bankCode, String customerNo, String beginDate, String endDate) {
        String url = ZoneHelper.getGoldApiUrl() + "/trade/getBuySellPoint";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        paraMap.put("beginDate", beginDate);
        paraMap.put("endDate", endDate);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<GetBuySellPointResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<GetBuySellPointResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, false);
    }

    /**
     * 获取短信验证码
     *
     * @param bankCode   二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param customerNo 用户编号
     * @param mobileTel  接收验证码手机号(不传默认使用二类卡手机号)
     * @param preApplyId 预申请Id
     * @return
     */
    @TraceMark
    public SendSmsResp sendSms(String bankCode, String customerNo, String mobileTel, String preApplyId) {
        String url = ZoneHelper.getGoldApiUrl() + "/common/sendSms";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        paraMap.put("mobileTel", mobileTel);
        paraMap.put("preApplyId", preApplyId);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<SendSmsResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<SendSmsResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 入金预申请
     *
     * @param customerNo 用户编号
     * @param bankCode   二类户所在行
     * @param appAmount  申请金额
     * @return
     */
    @TraceMark
    public CashInPreResp cashInPre(String customerNo, String bankCode, BigDecimal appAmount) {

        String url = ZoneHelper.getGoldApiUrl() + "/outInMoney/cashInPre";
        Map<String, Object> paraMap = new HashMap<>(4);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        paraMap.put("appAmount", appAmount);

        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<CashInPreResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<CashInPreResp>>() {
                }).getBody();

        return resolveSpecialResultInfo(midResultInfo, true);
    }

    /**
     * 入金
     *
     * @param customerNo 用户编号
     * @param bankCode   二类户所在行
     * @param preApplyId 预申请Id
     * @param smsCode    短信验证码（无需验证码验证不传）
     * @param smsId      短信Id（无需验证码验证不传）
     * @return
     */
    @TraceMark
    public CashInfo cashIn(String customerNo, String bankCode, String preApplyId, String smsCode, String smsId) {

        String url = ZoneHelper.getGoldApiUrl() + "/outInMoney/cashIn";
        Map<String, Object> paraMap = new HashMap<>(8);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        paraMap.put("preApplyId", preApplyId);
        SmsInfo smsInfo = new SmsInfo();
        smsInfo.setSmsCode(smsCode);
        smsInfo.setSmsId(smsId);
        paraMap.put("smsInfo", smsInfo);

        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<CashInfo> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<CashInfo>>() {
                }).getBody();

        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 出金预申请
     *
     * @param customerNo 用户编号
     * @param bankCode   二类户所在行
     * @param appAmount  申请金额
     * @return
     */
    @TraceMark
    public CashOutPreResp cashOutPre(String customerNo, String bankCode, BigDecimal appAmount) {

        String url = ZoneHelper.getGoldApiUrl() + "/outInMoney/cashOutPre";
        Map<String, Object> paraMap = new HashMap<>(4);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        paraMap.put("appAmount", appAmount);

        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<CashOutPreResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<CashOutPreResp>>() {
                }).getBody();

        return resolveSpecialResultInfo(midResultInfo, true);
    }

    /**
     * 出金
     *
     * @param customerNo 用户编号
     * @param bankCode   二类户所在行
     * @param preApplyId 预申请Id
     * @param smsCode    短信验证码（无需验证码验证不传）
     * @param smsId      短信Id（无需验证码验证不传）
     * @return
     */
    @TraceMark
    public List<CashInfo> cashOut(String customerNo, String bankCode, String preApplyId, String smsCode, String smsId) {

        String url = ZoneHelper.getGoldApiUrl() + "/outInMoney/cashOut";
        Map<String, Object> paraMap = new HashMap<>(8);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        paraMap.put("preApplyId", preApplyId);
        SmsInfo smsInfo = new SmsInfo();
        smsInfo.setSmsCode(smsCode);
        smsInfo.setSmsId(smsId);
        paraMap.put("smsInfo", smsInfo);

        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<List<CashInfo>> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<List<CashInfo>>>() {
                }).getBody();

        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 资金记录查询
     *
     * @param customerNo 用户编号
     * @param bankCode   二类户所在行
     * @param pageSize   页面大小。pageSize最大值为20，超过接口默认使用20
     * @param page       当前页数
     * @param beginDate  开始时间。两者相隔必须3月以内，如果超过，接口默认查询入参中终止时间到终止时间前3个月的记录
     * @param endDate    结束时间
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    public PaginationData<CashSimple> cashListQuery(String customerNo, String bankCode, Integer pageSize, Integer page,
                                                    String beginDate, String endDate) {

        String url = ZoneHelper.getGoldApiUrl() + "/outInMoney/cashListQuery";
        Map<String, Object> paraMap = new HashMap<>(8);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        paraMap.put("pageSize", pageSize);
        paraMap.put("page", page);
        paraMap.put("beginDate", beginDate);
        paraMap.put("endDate", endDate);

        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<PaginationData<CashSimple>> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<PaginationData<CashSimple>>>() {
                }).getBody();

        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 交易总览查询
     *
     * @param bankCode   二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param customerNo 用户编号
     * @param beginDate  查询开始时间
     * @param endDate    查询结束时间
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    public TradeSummaryQueryResp tradeSummaryQuery(String bankCode, String customerNo, String beginDate, String endDate) {
        String url = ZoneHelper.getGoldApiUrl() + "/trade/tradeSummaryQuery";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        paraMap.put("beginDate", beginDate);
        paraMap.put("endDate", endDate);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<TradeSummaryQueryResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<TradeSummaryQueryResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, false);
    }

    /**
     * 交易记录查询
     *
     * @param customerNo    用户编号
     * @param bankCode      二类户所在行
     * @param pageSize      页面大小。pageSize最大值为20，超过接口默认使用20
     * @param page          当前页数
     * @param beginDate     开始时间。两者相隔必须3月以内，如果超过，接口默认查询入参中终止时间到终止时间前3个月的记录
     * @param endDate       结束时间
     * @param appStateList  状态列表（1-成功，2-失败，3-处理中）
     * @param businTypeList 业务类型列表
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    public PaginationData<TradeInfoSimple> tradeListQuery(String customerNo, String bankCode, Integer pageSize, Integer page
            , String beginDate, String endDate, List<String> appStateList, List<String> businTypeList) {

        List<EnumAppState> enumAppStateList = null;
        if (!CollectionUtils.isEmpty(appStateList)) {
            enumAppStateList = new ArrayList<>();
            for (String appState : appStateList) {
                EnumAppState item = EnumAppState.getEnum(appState);
                if (item != null) {
                    enumAppStateList.add(item);
                }
            }
        }

        List<EnumBusinType> enumBusinTypeList = null;
        if (!CollectionUtils.isEmpty(businTypeList)) {
            enumBusinTypeList = new ArrayList<>();
            for (String businType : businTypeList) {
                EnumBusinType item = EnumBusinType.getEnum(businType);
                if (item != null) {
                    enumBusinTypeList.add(item);
                }
            }
        }

        String url = ZoneHelper.getGoldApiUrl() + "/trade/tradeListQuery";
        Map<String, Object> paraMap = new HashMap<>(8);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        paraMap.put("pageSize", pageSize);
        paraMap.put("page", page);
        paraMap.put("beginDate", beginDate);
        paraMap.put("endDate", endDate);
        paraMap.put("appStateList", enumAppStateList);
        paraMap.put("businTypeList", enumBusinTypeList);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<PaginationData<TradeInfoSimple>> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<PaginationData<TradeInfoSimple>>>() {
                }).getBody();

        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 交易详情查询
     *
     * @param appSerialNo 交易单号
     * @param bankCode    二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param customerNo  用户编号
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    @Cacheable(value = "goldTradeDetailQuery", cacheManager = "caffeineCacheManager")
    public TradeInfoDetail tradeDetailQuery(String appSerialNo, String bankCode, String customerNo) {
        String url = ZoneHelper.getGoldApiUrl() + "/trade/tradeDetailQuery";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("appSerialNo", appSerialNo);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<TradeInfoDetail> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<TradeInfoDetail>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, false);
    }

    @TraceMark
    public AccessResultCommitResp accessCommit(String customerNo, String bankCode, String questionnaireKey, List<AnswerInfo> answerInfoList) {
        String url = ZoneHelper.getGoldApiUrl() + "/account/accessCommit";
        Map<String, Object> paraMap = new HashMap<>(4, 1);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        paraMap.put("questionnaireKey", questionnaireKey);
        paraMap.put("answerInfoList", answerInfoList);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<AccessResultCommitResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<AccessResultCommitResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    public AccessQuestionQryResp getAssessQuestionnaire(String customerNo, String bankCode) {
        String url = ZoneHelper.getGoldApiUrl() + "/account/getAssessQuestionnaire";
        Map<String, String> paraMap = new HashMap<>(2, 1);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        HttpEntity<Map<String, String>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<AccessQuestionQryResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<AccessQuestionQryResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    @Cacheable(value = "getRiskAuthBook", cacheManager = "moderateCacheManager")
    public RiskAuthBookResp getRiskAuthBook(String customerNo, String bankCode) {
        String url = ZoneHelper.getGoldApiUrl() + "/account/getRiskAuthBook";
        Map<String, String> paraMap = new HashMap<>(2, 1);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        HttpEntity<Map<String, String>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<RiskAuthBookResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<RiskAuthBookResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    @TraceMark
    public RiskAuthResp riskAuth(String customerNo, String bankCode, String fileId) {
        String url = ZoneHelper.getGoldApiUrl() + "/account/riskAuth";
        Map<String, String> paraMap = new HashMap<>(4);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        paraMap.put("fileId", fileId);
        HttpEntity<Map<String, String>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<RiskAuthResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<RiskAuthResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, false);
    }

    @TraceMark
    public RiskPreCheckResp riskPreCheck(String customerNo, String bankCode) {
        String url = ZoneHelper.getGoldApiUrl() + "/account/riskPreCheck";
        Map<String, String> paraMap = new HashMap<>(2, 1);
        paraMap.put("customerNo", customerNo);
        paraMap.put("bankCode", bankCode);
        HttpEntity<Map<String, String>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<RiskPreCheckResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<RiskPreCheckResp>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 持仓摘要
     *
     * @param bankCode   二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param customerNo 用户编号
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    @Cacheable(value = "goldHoldShareSummary", cacheManager = "caffeineCacheManager")
    public HoldShareSummary getHoldShareSummary(String bankCode, String customerNo) {
        String url = ZoneHelper.getGoldApiUrl() + "/share/getHoldShareSummary";
        Map<String, Object> paraMap = new HashMap<>(2);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<HoldShareSummary> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<HoldShareSummary>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 总收益
     *
     * @param bankCode   二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param customerNo 用户编号
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    @Cacheable(value = "goldTotalProfitInfo", cacheManager = "oneMinuteCaffeineCacheManager")
    public TotalProfitInfo getTotalProfitInfo(String bankCode, String customerNo) {
        String url = ZoneHelper.getGoldApiUrl() + "/share/getTotalProfitInfo";
        Map<String, Object> paraMap = new HashMap<>(2);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<TotalProfitInfo> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<TotalProfitInfo>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, true);
    }

    /**
     * 收益趋势图
     *
     * @param bankCode   二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param customerNo 用户编号
     * @param beginDate  查询开始时间
     * @param endDate    查询结束时间
     * @param maxPointCount  最大点数
     * @param type 曲线图类型PROFIT-收益,PROFIT_RATE收益率,ASSET资产总额
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    @Cacheable(value = "goldProfitTrendChart", cacheManager = "oneMinuteCaffeineCacheManager")
    public ProfitTrendChart getProfitTrendChart(String bankCode, String customerNo, String beginDate, String endDate
            , Integer maxPointCount, String type) {
        String url = ZoneHelper.getGoldApiUrl() + "/share/getProfitTrendChart";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        paraMap.put("beginDate", beginDate);
        paraMap.put("endDate", endDate);
        paraMap.put("maxPointCount", maxPointCount);
        paraMap.put("type", EnumTrendChartType.valueOf(type));
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<ProfitTrendChart> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<ProfitTrendChart>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, false);
    }

    /**
     * 收益日历图
     *
     * @param bankCode   二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param customerNo 用户编号
     * @param beginDate  查询开始时间
     * @param endDate    查询结束时间
     * @param type  DAY(0),WEEK(1),MONTH(2),YEAR(3)
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    @Cacheable(value = "goldProfitCalendarChart", cacheManager = "oneMinuteCaffeineCacheManager")
    public ProfitCalendarChart getProfitCalendarChart(String bankCode, String customerNo, String beginDate, String endDate, Integer type) {
        String url = ZoneHelper.getGoldApiUrl() + "/share/getProfitCalendarChart";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        paraMap.put("beginDate", beginDate);
        paraMap.put("endDate", endDate);
        paraMap.put("type", EnumPeriodType.getEumByType(type));
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<ProfitCalendarChart> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<ProfitCalendarChart>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, false);
    }

    /**
     * 区间资产信息
     *
     * @param bankCode   二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param customerNo 用户编号
     * @param period  查询开始时间
     * @param type  DAY(0),WEEK(1),MONTH(2),YEAR(3)
     * @return
     */
    @TraceMark
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    @Cacheable(value = "goldPeriodAsset", cacheManager = "oneMinuteCaffeineCacheManager")
    public PeriodAsset getPeriodAsset(String bankCode, String customerNo, String period, Integer type) {
        String url = ZoneHelper.getGoldApiUrl() + "/share/getPeriodAsset";
        Map<String, Object> paraMap = new HashMap<>(6);
        paraMap.put("bankCode", bankCode);
        paraMap.put("customerNo", customerNo);
        paraMap.put("period", period);
        paraMap.put("type", EnumPeriodType.getEumByType(type));
        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<PeriodAsset> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<PeriodAsset>>() {
                }).getBody();
        return resolveResultInfo(midResultInfo, false);
    }

    public static <T> T resolveResultInfo(ResultInfo<T> resultInfo, boolean isResultMustNotNull) {
        if (resultInfo == null) {
            throw new GatewayException(EnumGatewayException.SYSTEM_INTERNAL_ANOMALY);
        }

        boolean isNull = isResultMustNotNull && resultInfo.getResult() == null;
        if (!resultInfo.isSuccess() || isNull) {
            log.warn("积存金接口调用失败:{}", resultInfo);
            throw new IbgException(EnumIbgBiz.COMMON_GW.getBizCode(), resultInfo.getErrorCode(), resultInfo.getMessage());
        }
        return resultInfo.getResult();
    }

    /**
     * 抛出中台特定 errorCode
     *
     * @param resultInfo
     * @param isResultMustNotNull
     * @return
     */
    public static <T> T resolveSpecialResultInfo(ResultInfo<T> resultInfo, boolean isResultMustNotNull) {
        if (resultInfo == null) {
            throw new GatewayException(EnumGatewayException.SYSTEM_INTERNAL_ANOMALY);
        }

        boolean isNull = isResultMustNotNull && resultInfo.getResult() == null;
        if (!resultInfo.isSuccess() || isNull) {
            log.warn("积存金接口调用失败:{}", resultInfo);
            try {
                Integer errorCode = Integer.valueOf(resultInfo.getErrorCode());
                throw new GatewayException(errorCode, resultInfo.getMessage());
            } catch (NumberFormatException e) {
                throw new IbgException(EnumIbgBiz.COMMON_GW.getBizCode(), resultInfo.getErrorCode(), resultInfo.getMessage());
            }
        }
        return resultInfo.getResult();
    }

    /**
     * 申购
     *
     * @param appAmount   申请金额
     * @param bankCode    二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param clientIp    客户端Ip
     * @param customerNo  用户编号
     * @param payType     支付方式
     * @param appPrice    金价
     * @param priceId     价格Id
     * @param productCode 产品代码
     * @return
     */
    @TraceMark
    public GoldPurchaseResp purchase(BigDecimal appAmount, String bankCode, String clientIp, String customerNo,
                                     String payType, BigDecimal appPrice, String priceId, String productCode) {

        String url = ZoneHelper.getGoldApiUrl() + "/trade/purchase";
        Map<String, Object> paraMap = new HashMap<>(16);
        paraMap.put("appAmount", appAmount);
        paraMap.put("bankCode", bankCode);
        paraMap.put("clientIp", clientIp);
        paraMap.put("customerNo", customerNo);
        paraMap.put("payType", EnumPayType.getEnum(payType));
        paraMap.put("productCode", productCode);

        PriceInfo priceInfo = new PriceInfo();
        priceInfo.setAppPrice(appPrice);
        priceInfo.setPriceId(priceId);
        paraMap.put("priceInfo", priceInfo);

        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<GoldPurchaseResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<GoldPurchaseResp>>() {
                }).getBody();

        return resolveSpecialResultInfo(midResultInfo, true);
    }


    /**
     * 赎回
     *
     * @param appVol      卖出份额（4位小数）
     * @param bankCode    二类户所在行(比如开民生的积存金账户，这里传民生的银行code)
     * @param clientIp    客户端Ip
     * @param customerNo  用户编号
     * @param businType   赎回方式（REDEEM、REDEEM_TO_BIND_CARD）
     * @param appPrice    金价
     * @param priceId     价格Id
     * @param productCode 产品代码
     * @return
     */
    @TraceMark
    public GoldRedeemResp redeem(BigDecimal appVol, String bankCode, String businType, String clientIp,
                                 String customerNo, BigDecimal appPrice, String priceId, String productCode) {

        String url = ZoneHelper.getGoldApiUrl() + "/trade/redeem";
        Map<String, Object> paraMap = new HashMap<>(16);
        paraMap.put("appVol", appVol);
        paraMap.put("bankCode", bankCode);
        paraMap.put("businType", EnumBusinType.getEnum(businType));
        paraMap.put("clientIp", clientIp);
        paraMap.put("customerNo", customerNo);
        paraMap.put("productCode", productCode);

        PriceInfo priceInfo = new PriceInfo();
        priceInfo.setAppPrice(appPrice);
        priceInfo.setPriceId(priceId);
        paraMap.put("priceInfo", priceInfo);

        HttpEntity<Map<String, Object>> request = GwBuildHelper.buildHttpEntity(paraMap, MediaType.APPLICATION_JSON, false);
        ResultInfo<GoldRedeemResp> midResultInfo = restTemplate.exchange(url, HttpMethod.POST, request,
                new ParameterizedTypeReference<ResultInfo<GoldRedeemResp>>() {
                }).getBody();

        return resolveSpecialResultInfo(midResultInfo, true);
    }

}
