package com.ttfund.ibg.ariesgateway.manager;

import com.ttfund.ibg.ariesgateway.util.ZoneHelper;
import com.ttfund.ibg.gateway.common.exception.GatewayException;
import com.ttfund.ibg.gateway.common.model.enums.EnumGatewayException;
import com.ttfund.ibg.gateway.core.util.GwBuildHelper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * tfs隐私服务
 *
 * <AUTHOR>
 * @date 2025/4/9
 */
@Slf4j
@Component
public class FileManager {

    @Resource
    private RestTemplate restTemplate;

    /**
     * 文件上传
     *
     * @param fileResource 文件的Resource表示形式(如{@link MultipartFile#getResource()})
     * @return 中台返回的文件Id
     */
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 100L))
    public String fileSave(org.springframework.core.io.Resource fileResource) {
        String url = ZoneHelper.getTsfApiUrl() + "/save";
        MultiValueMap<String, Object> paramMap = new LinkedMultiValueMap<>();
        paramMap.add("file", fileResource);
        HttpEntity<MultiValueMap<String, Object>> httpEntity = GwBuildHelper.buildHttpEntity(paramMap, MediaType.MULTIPART_FORM_DATA, false);
        FilerResultInfo<String> resultInfo = restTemplate.exchange(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<FilerResultInfo<String>>() {
        }).getBody();

        if (resultInfo == null) {
            throw new GatewayException(EnumGatewayException.PENSION_FUND_INVOKE_FAIL, "临时文件存储系统文件上传异常");
        }
        if (!resultInfo.getSucceed()) {
            log.warn("临时文件存储系统调用失败:{}", resultInfo);
            throw new GatewayException(EnumGatewayException.PENSION_FUND_INVOKE_FAIL, resultInfo.getMessage());
        }

        return resultInfo.getResult();
    }


    /**
     * 临时文件存储系统接口返回结果包装类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class FilerResultInfo<T> {
        private String codeMessage;
        private String errorCode;
        private String message;
        private T result;
        private Boolean succeed;
    }
}
