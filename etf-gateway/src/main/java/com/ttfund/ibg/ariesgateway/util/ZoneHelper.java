package com.ttfund.ibg.ariesgateway.util;

import cn.hutool.core.util.StrUtil;
import com.ttfund.ibg.ariesgateway.config.apollo.AddressConfigReader;
import com.ttfund.ibg.ariesgateway.manager.AccountManager;
import com.ttfund.ibg.gateway.common.exception.GatewayException;
import com.ttfund.ibg.gateway.common.model.enums.EnumGatewayException;
import com.ttfund.ibg.gateway.core.context.TraceHelper;
import com.ttfund.ibg.gateway.core.context.TraceInfo;
import lombok.Data;

/**
 * <AUTHOR> <EMAIL>
 * @date 2023/7/3
 */
public class ZoneHelper {

    private static final AccountManager ACCOUNT_MANAGER = SpringFactory.getBean(AccountManager.class);

    private static final AddressConfigReader addressConfigReader = SpringFactory.getBean(AddressConfigReader.class);




    @Data
    public static class UrlAndZone {

        private String url;

        private String zone;

    }

    /**
     * 获取用户核心业务分区地址
     *
     * @param investorId 用户编号
     * @return 分区地址
     */
    public static String getUserCoreApiUrl(String investorId) {
        String zone = ACCOUNT_MANAGER.getRouteZoneFromTraceInfo(investorId);
        if (addressConfigReader.getCoreUserApi() == null) {
            throw new RuntimeException("Apollo配置core-user-api为null");
        }
        if (addressConfigReader.getCoreUserApi().get(zone) == null) {
            return getDefaultUserCoreApi();
        }
        return addressConfigReader.getCoreUserApi().get(zone);
    }

    /**
     * 获取积存金中台地址
     *
     * @return 地址
     */
    public static String getGoldApiUrl() {
        return addressConfigReader.getGoldApi();
    }

    /**
     * 临时文件存储系统地址(王文昊）
     *
     * @return 地址
     */
    public static String getTsfApiUrl() {
        return addressConfigReader.getTsffApi();
    }

    /**
     * 取默认分区，Apollo配置0为默认地址
     *
     * @return 默认分区地址
     */
    public static String getDefaultUserCoreApi() {
        return addressConfigReader.getCoreUserApi().get("0");
    }
}
