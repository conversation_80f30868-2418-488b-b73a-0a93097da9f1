local function FGUtilStringSplit(str, split_char)
    local sub_str_tab = {};
    while (true) do
        local pos = string.find(str, split_char);
        if (not pos) then
            sub_str_tab[#sub_str_tab + 1] = str;
            break ;
        end
        local sub_str = string.sub(str, 1, pos - 1);
        sub_str_tab[#sub_str_tab + 1] = sub_str;
        str = string.sub(str, pos + 1, #str);
    end
    return sub_str_tab;
end

local key = KEYS
local hkey = ARGV[1]
local hkeys = FGUtilStringSplit(hkey, "~")
local size = tonumber(ARGV[2])
local res = ""
for i = 1, size do
    res = res .. redis.call('hget', key[1], hkeys[i]) .. "~"
end
return string.sub(res, 1, string.len(res) - 1)