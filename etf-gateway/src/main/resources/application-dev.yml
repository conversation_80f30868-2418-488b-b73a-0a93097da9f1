apollo:
  meta: http://10.228.130.158:8080
  # 测试一下指定集群
  bootstrap:
    eagerLoad:
      enabled: true
    enabled: true
  redis:
    enabled: true
    source:
      name: ApiCommon
      timeout: 10s
      lettuce:
        pool:
          max-active: 20
          max-idle: 20
          min-idle: 10
          time-between-eviction-runs: 60s
        cluster:
          refresh:
            adaptive: true
            period: 60s

sys:
  zone: -1

rpc:
  aries:
    # 服务坐标
    coordinates:
      - unique-id: local1
        zone: 1

sentinel-dashboard: http://localhost:8080
