apollo:
  meta: http://10.150.158.16:8080
  cluster: test
  bootstrap:
    namespaces: application
    eagerLoad:
      enabled: true
    enabled: true
  # 禁用自动发现的 namespace
  autoUpdateInjectedSpringProperties: false

server:
  port: 80

# 系统配置
sys:
  zone: -1
  alarm:
    maxQueueSize: 1000
    appName: aries-gateway-test
    notice-instance: 60000
  security-reference: aries-gateway

# 基础配置，防止 null 值
rpc:
  uniqueId: test-gateway

# 日志路径配置
log:
  path: ./logs

# Sentinel 配置
csp:
  sentinel:
    api:
      port: 8720

# 地址配置（原 address_config namespace 的内容）
sentinel-dashboard: http://localhost:8080
zookeeper: localhost:2181

# 核心服务地址配置
core-user-api:
  "0": http://localhost:8080  # 默认分区
  "1": http://localhost:8080  # 分区1
  default: http://localhost:8080

business-interface-api: http://localhost:8080
gold-api: http://localhost:8080
# 基金通服务地址（test 环境暂时禁用）
# aries-account-url: null
# aries-assets-url: null
# aries-business-url: null
# aries-query-url: null
# aries-product-url: null
# 监控服务已移除，不再配置 aries-monitor-url

# 禁用监控功能
monitor:
  enabled: false

# 禁用报警规则同步
alarm:
  rule:
    sync:
      enabled: false

# 设置无效的监控地址，避免 null 值导致的 URL 拼接错误
aries-monitor-url: "http://disabled-monitor-service"