<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="10 seconds">

    <property name="pattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{request_url}],[%X{request_trace_id}],[%X{requester_origin}],[%X{requester_ip}],[%X{request_method}],[%X{request_elapsed}],[%X{request_token}],[%X{request_param}],[%X{request_result}],[%X{request_detail}],[%X{request_error}] %logger{35} - %msg%n"/>

    <!-- 配置logstash不同环境参数 -->
    <springProfile name="dev">
        <include resource="org/springframework/boot/logging/logback/base.xml"/>
        <property name="appsuffix" value="aries_gateway_dev"/>
        <property name="logstash_home" value="**************"/>
        <property name="logstash_port" value="5690"/>
    </springProfile>

    <springProfile name="test">
        <property name="appsuffix" value="aries_gateway_test"/>
        <property name="logstash_home" value="ls4.elk.yunwei"/>
        <property name="logstash_port" value="5727"/>
    </springProfile>

    <springProfile name="uat">
        <property name="appsuffix" value="aries_gateway_uat"/>
        <property name="logstash_home" value="ls4.elk.yunwei"/>
        <property name="logstash_port" value="5727"/>
    </springProfile>

    <springProfile name="beta">
        <property name="appsuffix" value="aries_gateway_beta"/>
        <property name="logstash_home" value="ls4.elk.yunwei"/>
        <property name="logstash_port" value="5727"/>
    </springProfile>

    <springProfile name="prod">
        <property name="appsuffix" value="aries_gateway_prod"/>
        <property name="logstash_home" value="ls4.elk.yunwei"/>
        <property name="logstash_port" value="5727"/>
    </springProfile>

    <springProperty scope="context" name="log.path" source="log.path"/>

    <!-- SkyWalking TraceId 转换器 -->
    <conversionRule conversionWord="tid"
                    converterClass="org.apache.skywalking.apm.toolkit.log.logback.v1.x.LogbackPatternConverter"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoders are assigned the type ch.qos.logback.classic.encoder.PatternLayoutEncoder by default -->
        <encoder>
            <pattern>${pattern}</pattern>
        </encoder>
    </appender>

    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>
            <onMismatch>ACCEPT</onMismatch>
        </filter>
        <File>${log.path}/info.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/info-%d{yyyyMMdd}.log.%i</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>500MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>3</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>${pattern}</Pattern>
        </layout>
    </appender>

    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <File>${log.path}/error.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/error-%d{yyyyMMdd}.log.%i</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>500MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>3</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>${pattern}</Pattern>
        </layout>
    </appender>

    <!-- 配置logstash info日志 -->
    <appender name="info_stash" class="net.logstash.logback.appender.LogstashUdpSocketAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>
            <onMismatch>ACCEPT</onMismatch>
        </filter>
        <!--logstash服务器IP -->
        <host>${logstash_home}</host>
        <!-- port is optional (default value shown) -->
        <port>${logstash_port}</port>
        <layout class="net.logstash.logback.layout.LoggingEventCompositeJsonLayout">
            <providers>
                <mdc/>
                <logstashMarkers/>
                <pattern>
                    <pattern>
                        {
                        "logTime": "%d{yyyy-MM-dd'T'HH:mm:ss.SSS0000XXX}",
                        "logLevel": "%level",
                        "appName": "${appsuffix}",
                        "thread": "%thread",
                        "logger": "%logger{0}",
                        "message": "%message",
                        "stackTrace": "%exception{full}",
                        "spanId": "%X{SOFA-SpanId:-}",
                        "traceId": "%tid",
                        "requestTraceId": "%X{request_trace_id:-}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </layout>
    </appender>

    <!-- 配置logstash error日志 -->
    <appender name="error_stash" class="net.logstash.logback.appender.LogstashUdpSocketAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <!--logstash服务器IP -->
        <host>${logstash_home}</host>
        <!-- port is optional (default value shown) -->
        <port>${logstash_port}</port>
        <layout class="net.logstash.logback.layout.LoggingEventCompositeJsonLayout">
            <providers>
                <mdc/>
                <logstashMarkers/>
                <pattern>
                    <pattern>
                        {
                        "logTime": "%d{yyyy-MM-dd'T'HH:mm:ss.SSS0000XXX}",
                        "logLevel": "%level",
                        "appName": "${appsuffix}",
                        "thread": "%thread",
                        "logger": "%logger{0}",
                        "message": "%message",
                        "stackTrace": "%exception{full}",
                        "spanId": "%X{SOFA-SpanId:-}",
                        "traceId": "%tid",
                        "requestTraceId": "%X{request_trace_id:-}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </layout>
    </appender>

    <!-- 针对项目特定包的日志配置 -->
    <logger name="com.ttfund.ibg.ariesgateway" level="DEBUG" additivity="false">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="info_stash"/>
        <appender-ref ref="error_stash"/>
    </logger>

    <!-- Apollo配置中心日志 -->
    <logger name="com.ctrip.framework.apollo" level="INFO" additivity="false">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="info_stash"/>
        <appender-ref ref="error_stash"/>
    </logger>


    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="INFO_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
            <appender-ref ref="info_stash"/>
            <appender-ref ref="error_stash"/>
        </root>
    </springProfile>

    <springProfile name="test">
        <root level="INFO">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="INFO_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
            <appender-ref ref="info_stash"/>
            <appender-ref ref="error_stash"/>
        </root>
    </springProfile>

    <springProfile name="uat">
        <root level="INFO">
            <!-- <appender-ref ref="STDOUT" /> -->
            <appender-ref ref="INFO_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
            <appender-ref ref="info_stash"/>
            <appender-ref ref="error_stash"/>
        </root>
    </springProfile>

    <springProfile name="beta">
        <root level="INFO">
            <!-- <appender-ref ref="STDOUT" /> -->
            <appender-ref ref="INFO_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
            <appender-ref ref="info_stash"/>
            <appender-ref ref="error_stash"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <root level="INFO">
            <!-- <appender-ref ref="STDOUT" /> -->
            <appender-ref ref="INFO_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
            <appender-ref ref="info_stash"/>
            <appender-ref ref="error_stash"/>
        </root>
    </springProfile>



</configuration>